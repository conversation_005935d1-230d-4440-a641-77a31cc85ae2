!function(e){var n={};function t(r){if(n[r])return n[r].exports;var a=n[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,t),a.l=!0,a.exports}t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var a in e)t.d(r,a,function(n){return e[n]}.bind(null,a));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=95)}([function(e,n,t){"use strict";t(64),t(75),t(2),t(32),t(56),t(34),t(57);var r={isInApp:function(){return navigator.userAgent.indexOf("unicom")>-1},isIOS:function(){var e=!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return e},isAndroid:function(){var e=navigator.userAgent,n=e.indexOf("Android")>-1||e.indexOf("Adr")>-1;return n},appVersion:function(){var e=window.navigator.userAgent.match(/unicom{version:([^},]+)+/);if(e&&e.length>1){var n=e[1];return n.split("@")[1]}var t=this.getCookieFromDocument("c_version");return t?t.split("@")[1]:(console.warn("获取不到版本号，默认返回8.0000"),"8.0000")},execIOSJSBridge:function(e){var n=document.createElement("iframe");n.src=e,n.style.display="none",document.body.appendChild(n),window.setTimeout((function(){document.body.removeChild(n)}),1e3)},getCookieFromDocument:function(e){var n,t=new RegExp("(^| )"+e+"=([^;]*)(;|$)");if(n=document.cookie.match(t))return unescape(n[2]);var r=document.URL;r=(r=r.substring(r.indexOf("?"))).replace(/#\//gi,"&").replace(/\?/gi,"&"),t=new RegExp("/".concat(e,"=([^&]+)($|[^&])/"));var a=r.match(t);return a?unescape(a[2]):null}};n.a=r},function(e,n,t){"use strict";t(61),t(3),t(2),t(32);var r=t(0),a=window.MsJSBridge={MsUniqueId:1,callbacks:{},eventListenerStorage:{},exec:function(e,n,t,i,o){if(r.a.isInApp()){!0!==o&&(o=!1);var s={action:e,parameter:n,isKeepAlive:o},c={};if("function"==typeof t&&(c.success=t),"function"==typeof i&&(c.fail=i),Object.keys(c).length){var u=a.getUniqueId();a.callbacks[u]=c,s.callbackId=u}var l=JSON.stringify(s);r.a.isAndroid()?AndroidMsJSBridge.exec(l):r.a.isIOS()&&window.webkit.messageHandlers.MsJSBridge.postMessage(l)}else i("端外不支持"+e+"方法调用！")},execSync:function(e,n){if(!e)return console.debug("调用同步方法传入的action为空"),null;var t={action:e,parameter:n||""},a=JSON.stringify(t),i="";if(!r.a.isInApp())return"端外不支持（"+e+"）方法调用";if(i=r.a.isAndroid()?AndroidMsJSBridge.execSync(a):window.prompt(a,"MsJSBridge"))try{var o=decodeURIComponent(atob(i));return JSON.parse(o).data}catch(e){console.error("MsJSBridge-receiveEventActionFromNative-报错："+e)}},callbackFromNative:function(e){try{var n=decodeURIComponent(atob(e)),t=JSON.parse(n),r=a.callbacks[t.callbackId],i=t.parameter,o=t.isKeepAlive;"success"==i.status&&r&&r.success?r.success(i.data,o):"fail"==i.status&&r&&r.fail&&(o=!1,r.fail(i.data,o)),o||delete a.callbacks[t.callbackId]}catch(e){console.error("MsJSBridge-callbackFromNative-报错："+e)}},addEventListener:function(e,n){var t=this.eventListenerStorage[e];t||(t={},this.eventListenerStorage[e]=t),t[this.getUniqueId()]=n},removeEventListener:function(e,n){n||delete this.eventListenerStorage[e];var t=this.eventListenerStorage[e];t&&delete t[e]},receiveEventActionFromNative:function(e){try{var n=decodeURIComponent(atob(e)),t=JSON.parse(n),r=t.action,a=t.parameter||"",i=this.eventListenerStorage[r];if(!i)return;for(var o in i){var s=i[o];s&&s(a)}}catch(e){console.error("MsJSBridge-receiveEventActionFromNative-报错："+e)}},getUniqueId:function(){return a.MsUniqueId++,"MsJSBridge_"+a.MsUniqueId.toString()+"_"+(new Date).getTime()}};n.a=a},function(e,n,t){"use strict";var r=t(12),a=t(23);r({target:"RegExp",proto:!0,forced:/./.exec!==a},{exec:a})},function(e,n,t){var r=t(36),a=t(20),i=t(88);r||a(Object.prototype,"toString",i,{unsafe:!0})},function(e,n){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,n,t){(function(n){var t=function(e){return e&&e.Math==Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof n&&n)||Function("return this")()}).call(this,t(65))},function(e,n,t){var r=t(10);e.exports=function(e){if(!r(e))throw TypeError(String(e)+" is not an object");return e}},function(e,n,t){var r=t(5),a=t(43),i=t(11),o=t(45),s=t(54),c=t(79),u=a("wks"),l=r.Symbol,f=c?l:l&&l.withoutSetter||o;e.exports=function(e){return i(u,e)||(s&&i(l,e)?u[e]=l[e]:u[e]=f("Symbol."+e)),u[e]}},function(e,n,t){var r=t(4);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(e,n){e.exports=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e}},function(e,n){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,n){var t={}.hasOwnProperty;e.exports=function(e,n){return t.call(e,n)}},function(e,n,t){var r=t(5),a=t(26).f,i=t(13),o=t(20),s=t(28),c=t(70),u=t(52);e.exports=function(e,n){var t,l,f,p,g,_=e.target,d=e.global,h=e.stat;if(t=d?r:h?r[_]||s(_,{}):(r[_]||{}).prototype)for(l in n){if(p=n[l],f=e.noTargetGet?(g=a(t,l))&&g.value:t[l],!u(d?l:_+(h?".":"#")+l,e.forced)&&void 0!==f){if(typeof p==typeof f)continue;c(p,f)}(e.sham||f&&f.sham)&&i(p,"sham",!0),o(t,l,p,e)}}},function(e,n,t){var r=t(8),a=t(19),i=t(37);e.exports=r?function(e,n,t){return a.f(e,n,i(1,t))}:function(e,n,t){return e[n]=t,e}},function(e,n,t){var r=t(22),a=Math.min;e.exports=function(e){return e>0?a(r(e),9007199254740991):0}},function(e,n,t){"use strict";var r=t(12),a=t(59);r({target:"Array",proto:!0,forced:[].forEach!=a},{forEach:a})},function(e,n,t){var r=t(5),a=t(92),i=t(59),o=t(13);for(var s in a){var c=r[s],u=c&&c.prototype;if(u&&u.forEach!==i)try{o(u,"forEach",i)}catch(e){u.forEach=i}}},function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"c",(function(){return showShareMenu})),__webpack_require__.d(__webpack_exports__,"b",(function(){return shareSingleChannel})),__webpack_require__.d(__webpack_exports__,"d",(function(){return updateClientMoreMenu})),__webpack_require__.d(__webpack_exports__,"a",(function(){return openWxMiniProgram})),__webpack_require__.d(__webpack_exports__,"e",(function(){return updateEdopShareMenu}));var core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(15),core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(60),core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(3),core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(2),core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(56),core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(34),core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(57),core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(63),core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_7__),core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(16),core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_8___default=__webpack_require__.n(core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_8__),_comment_ms_jsbridge_js__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(1),_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(0);function openWxMiniProgram(e){if(_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isInApp()){if(!(e&&e.miniProgramUserName&&e.miniProgramPath&&e.miniProgramType))return void alert("打开小程序必须配置miniProgramUserName、miniProgramPath、miniProgramType参数信息");var n={type:_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isIOS()?"WXMiniProgramPay":"miniProgramOpen",shareJson:{miniProgramUserName:e.miniProgramUserName,miniProgramPath:e.miniProgramPath,miniProgramType:e.miniProgramType}};_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(n))}else console.warn("端外不支持调用openWxMiniProgram。")}function handleShareResult(resolve,res){var count,reg="/target/gi";if(reg=eval(reg),count=null==res.match(reg)?0:res.match(reg).length,count>1){var arr=res.split(",");arr[2]=arr[2].replace("target","token"),res=arr.join(",")}resolve(JSON.parse(res))}function showShareMenu(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isInApp()?new Promise((function(n,t){if(e){if("image"!=e.shareStyle&&!(e.shareStyle&&e.shareTitle&&e.shareContent&&e.shareIconURL&&e.shareURL))return void alert("shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。");var r="wechat,wechatmoments,qq,qzone,sinaweibo,shortmessage,wokouling,huabaofenxiang";if(e.shareChannel&&Array.isArray(e.shareChannel)&&e.shareChannel.length>0){if(1==e.shareChannel.length)return void alert("调用showShareMenu方法，如果配置了shareChannel参数，则至少要配置两个分享渠道");r=e.shareChannel.join(",")}var a={shareTitle:e.shareTitle,shareContent:e.shareContent,shareIconURL:e.shareIconURL,shareURL:e.shareURL,provider:e.provider?e.provider:"10010",businessCode:e.businessCode?e.businessCode:"9991111"};if(e.shareStyle&&"webpage"==e.shareStyle)r=r.replace("wokouling","").replace("huabaofenxiang",""),a.shareType="url";else if(e.shareStyle&&"pictorial"==e.shareStyle){if(!e.huabaoIconUrl)return void alert("当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");a.shareType="url";var i=e.huabaoIconUrl;i.startsWith("http")||(i=i.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.huabaoIconUrl=i}else{if(!e.shareStyle||"image"!=e.shareStyle)return void alert("shareStyle属性只能配置webpage、pictorial、image");if(!e.shareImage)return void alert("当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");r=r.replace("wokouling","").replace("huabaofenxiang","").replace("shortmessage",""),a.shareType="image";var o=e.shareImage;o.startsWith("http")||(o=o.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.templetImg=o}if(e.miniProgramShare&&"1"==e.miniProgramShare){if(!(e.miniProgramType&&e.miniProgramUserName&&e.miniProgramPath))return void alert("当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");a.miniProgramShare=e.miniProgramShare,a.miniProgramType=e.miniProgramType,a.miniProgramUserName=e.miniProgramUserName,a.miniProgramPath=e.miniProgramPath}var s={type:"share2",shareList:r,shareJson:a};window.setShareStatus_Local=function(e){handleShareResult(n,e)},_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}else alert("分享配置不能为空")})):(console.warn("端外不支持调用showShareMenu方法。"),new Promise((function(e,n){var t=new Error;t.data="端外不支持调用showShareMenu方法。",n(t)})))}function shareSingleChannel(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isInApp()?new Promise((function(n,t){if("image"==e.shareStyle||e.shareStyle&&e.shareTitle&&e.shareContent&&e.shareIconURL&&e.shareURL)if(e.shareChannel&&Array.isArray(e.shareChannel)&&e.shareChannel.length>0){var r=e.shareChannel[0],a={shareTitle:e.shareTitle,shareContent:e.shareContent,shareIconURL:e.shareIconURL,shareURL:e.shareURL,provider:e.provider?e.provider:"10010",businessCode:e.businessCode?e.businessCode:"9991111"};if(e.shareStyle&&"webpage"==e.shareStyle){if("wokouling"==r||"huabaofenxiang"==r)return void alert("当shareStyle配置为webpage时，shareChannel不能配置wokouling、huabaofenxiang，请检查配置项。");a.shareType="url"}else if(e.shareStyle&&"pictorial"==e.shareStyle){if(!e.huabaoIconUrl)return void alert("当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");a.shareType="url";var i=e.huabaoIconUrl;i.startsWith("http")||(i=i.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.huabaoIconUrl=i}else{if(!e.shareStyle||"image"!=e.shareStyle)return void alert("shareStyle属性只能配置webpage、pictorial、image");if(!e.shareImage)return void alert("当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");if("wokouling"==r||"huabaofenxiang"==r||"shortmessage"==r)return void alert("当shareStyle配置为image时，shareChannel不能配置wokouling、huabaofenxiang、shortmessage，请检查配置项。");a.shareType="image";var o=e.shareImage;o.startsWith("http")||(o=o.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.templetImg=o}if(e.miniProgramShare&&"1"==e.miniProgramShare){if(!(e.miniProgramType&&e.miniProgramUserName&&e.miniProgramPath))return void alert("当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");a.miniProgramShare=e.miniProgramShare,a.miniProgramType=e.miniProgramType,a.miniProgramUserName=e.miniProgramUserName,a.miniProgramPath=e.miniProgramPath}var s={type:"shareDirectly",shareList:r,shareJson:a};window.setShareStatus_Local=function(e){handleShareResult(n,e)},_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}else alert("shareChannel类型必须为数组且不能为空");else alert("shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。")})):(console.warn("端外不支持调用shareSingleChannel方法。"),new Promise((function(e,n){var t=new Error;t.data="端外不支持调用shareSingleChannel方法。",n(t)})))}function updateClientMoreMenu(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isInApp()?new Promise((function(n,t){if(e){var r=null,a=null,i=null;if(e.shareConfig){var o=e.shareConfig;if("image"!=o.shareStyle&&!(o.shareStyle&&o.shareTitle&&o.shareContent&&o.shareIconURL&&o.shareURL))return void alert("调用updateClientMoreMenu方法错误：shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。");if(r="wechat,wechatmoments,qq,qzone,sinaweibo,shortmessage,wokouling,huabaofenxiang",o.shareChannel&&Array.isArray(o.shareChannel)&&o.shareChannel.length>0){if(1==o.shareChannel.length)return void alert("调用updateClientMoreMenu方法错误：如果配置了shareChannel参数，则至少要配置两个分享渠道");r=o.shareChannel.join(",")}if(a={shareTitle:o.shareTitle,shareContent:o.shareContent,shareIconURL:o.shareIconURL,shareURL:o.shareURL,provider:o.provider?o.provider:"10010",businessCode:o.businessCode?o.businessCode:"9991111"},o.shareStyle&&"webpage"==o.shareStyle)r=r.replace("wokouling","").replace("huabaofenxiang",""),a.shareType="url";else if(o.shareStyle&&"pictorial"==o.shareStyle){if(!o.huabaoIconUrl)return void alert("调用updateClientMoreMenu方法错误：当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");a.shareType="url";var s=o.huabaoIconUrl;s.startsWith("http")||(s=s.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.huabaoIconUrl=s}else{if(!o.shareStyle||"image"!=o.shareStyle)return void alert("调用updateClientMoreMenu方法错误：shareStyle属性只能配置webpage、pictorial、image");if(!o.shareImage)return void alert("调用updateClientMoreMenu方法错误：当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");r=r.replace("wokouling","").replace("huabaofenxiang","").replace("shortmessage",""),a.shareType="image";var c=o.shareImage;c.startsWith("http")||(c=c.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.templetImg=c}if(o.miniProgramShare&&"1"==o.miniProgramShare){if(!(o.miniProgramType&&o.miniProgramUserName&&o.miniProgramPath))return void alert("调用updateClientMoreMenu方法错误：当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");a.miniProgramShare=o.miniProgramShare,a.miniProgramType=o.miniProgramType,a.miniProgramUserName=o.miniProgramUserName,a.miniProgramPath=o.miniProgramPath}i={shareType:"longScreenshot",shareQrcodeURL:o.shareQrcodeURL,shareTitle:a.shareTitle,shareContent:a.shareContent,shareURL:a.shareURL,shareIconURL:a.shareIconURL}}var u=[],l=["share","screenshotShare","feedback","goHome"];e.moreMenu&&Array.isArray(e.moreMenu)&&e.moreMenu.length>0&&(l=e.moreMenu),l.forEach((function(e){"share"==e?u.push({code:"fenxiang",title:"分享",shareList:r,shareJson:a}):"screenshotShare"==e?u.push({code:"jietufenxiang",title:"截图分享",shareList:r,shareJson:i}):"feedback"==e?u.push({code:"tucao",title:"反馈与建议",desc:"反馈与建议"}):"goHome"==e&&u.push({code:"shouye",title:"首页",desc:"回到首页"})}));var f={config:u};window.getMenuConfig_Local=function(){return JSON.stringify(f)}}window.setShareStatus_Local=function(e){handleShareResult(n,e)}})):(console.warn("端外不支持调用updateClientMoreMenu方法。"),new Promise((function(e,n){var t=new Error;t.data="端外不支持调用updateClientMoreMenu方法。",n(t)})))}function updateEdopShareMenu(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_10__.a.isInApp()?new Promise((function(n,t){window.setShareStatus_updateEdopShareMenu=function(e){handleShareResult(n,JSON.stringify(e))},_comment_ms_jsbridge_js__WEBPACK_IMPORTED_MODULE_9__.a.exec("updateEdopShareMenu",{shareConfig:e})})):(console.warn("端外不支持调用updateEdopShareMenu方法。"),new Promise((function(e,n){var t=new Error;t.data="端外不支持调用updateEdopShareMenu方法。",n(t)})))}},function(e,n){var t={}.toString;e.exports=function(e){return t.call(e).slice(8,-1)}},function(e,n,t){var r=t(8),a=t(39),i=t(6),o=t(38),s=Object.defineProperty;n.f=r?s:function(e,n,t){if(i(e),n=o(n,!0),i(t),a)try{return s(e,n,t)}catch(e){}if("get"in t||"set"in t)throw TypeError("Accessors not supported");return"value"in t&&(e[n]=t.value),e}},function(e,n,t){var r=t(5),a=t(13),i=t(11),o=t(28),s=t(40),c=t(42),u=c.get,l=c.enforce,f=String(String).split("String");(e.exports=function(e,n,t,s){var c=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,p=!!s&&!!s.noTargetGet;"function"==typeof t&&("string"!=typeof n||i(t,"name")||a(t,"name",n),l(t).source=f.join("string"==typeof n?n:"")),e!==r?(c?!p&&e[n]&&(u=!0):delete e[n],u?e[n]=t:a(e,n,t)):u?e[n]=t:o(n,t)})(Function.prototype,"toString",(function(){return"function"==typeof this&&u(this).source||s(this)}))},function(e,n,t){var r=t(27),a=t(9);e.exports=function(e){return r(a(e))}},function(e,n){var t=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:t)(e)}},function(e,n,t){"use strict";var r,a,i=t(31),o=t(55),s=RegExp.prototype.exec,c=String.prototype.replace,u=s,l=(r=/a/,a=/b*/g,s.call(r,"a"),s.call(a,"a"),0!==r.lastIndex||0!==a.lastIndex),f=o.UNSUPPORTED_Y||o.BROKEN_CARET,p=void 0!==/()??/.exec("")[1];(l||p||f)&&(u=function(e){var n,t,r,a,o=this,u=f&&o.sticky,g=i.call(o),_=o.source,d=0,h=e;return u&&(-1===(g=g.replace("y","")).indexOf("g")&&(g+="g"),h=String(e).slice(o.lastIndex),o.lastIndex>0&&(!o.multiline||o.multiline&&"\n"!==e[o.lastIndex-1])&&(_="(?: "+_+")",h=" "+h,d++),t=new RegExp("^(?:"+_+")",g)),p&&(t=new RegExp("^"+_+"$(?!\\s)",g)),l&&(n=o.lastIndex),r=s.call(u?t:o,h),u?r?(r.input=r.input.slice(d),r[0]=r[0].slice(d),r.index=o.lastIndex,o.lastIndex+=r[0].length):o.lastIndex=0:l&&r&&(o.lastIndex=o.global?r.index+r[0].length:n),p&&r&&r.length>1&&c.call(r[0],t,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(r[a]=void 0)})),r}),e.exports=u},function(e,n,t){"use strict";t(2);var r=t(20),a=t(4),i=t(7),o=t(23),s=t(13),c=i("species"),u=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),l="$0"==="a".replace(/./,"$0"),f=i("replace"),p=!!/./[f]&&""===/./[f]("a","$0"),g=!a((function(){var e=/(?:)/,n=e.exec;e.exec=function(){return n.apply(this,arguments)};var t="ab".split(e);return 2!==t.length||"a"!==t[0]||"b"!==t[1]}));e.exports=function(e,n,t,f){var _=i(e),d=!a((function(){var n={};return n[_]=function(){return 7},7!=""[e](n)})),h=d&&!a((function(){var n=!1,t=/a/;return"split"===e&&((t={}).constructor={},t.constructor[c]=function(){return t},t.flags="",t[_]=/./[_]),t.exec=function(){return n=!0,null},t[_](""),!n}));if(!d||!h||"replace"===e&&(!u||!l||p)||"split"===e&&!g){var m=/./[_],v=t(_,""[e],(function(e,n,t,r,a){return n.exec===o?d&&!a?{done:!0,value:m.call(n,t,r)}:{done:!0,value:e.call(t,n,r)}:{done:!1}}),{REPLACE_KEEPS_$0:l,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:p}),y=v[0],S=v[1];r(String.prototype,e,y),r(RegExp.prototype,_,2==n?function(e,n){return S.call(e,this,n)}:function(e){return S.call(e,this)})}f&&s(RegExp.prototype[_],"sham",!0)}},function(e,n,t){var r=t(18),a=t(23);e.exports=function(e,n){var t=e.exec;if("function"==typeof t){var i=t.call(e,n);if("object"!=typeof i)throw TypeError("RegExp exec method returned something other than an Object or null");return i}if("RegExp"!==r(e))throw TypeError("RegExp#exec called on incompatible receiver");return a.call(e,n)}},function(e,n,t){var r=t(8),a=t(66),i=t(37),o=t(21),s=t(38),c=t(11),u=t(39),l=Object.getOwnPropertyDescriptor;n.f=r?l:function(e,n){if(e=o(e),n=s(n,!0),u)try{return l(e,n)}catch(e){}if(c(e,n))return i(!a.f.call(e,n),e[n])}},function(e,n,t){var r=t(4),a=t(18),i="".split;e.exports=r((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==a(e)?i.call(e,""):Object(e)}:Object},function(e,n,t){var r=t(5),a=t(13);e.exports=function(e,n){try{a(r,e,n)}catch(t){r[e]=n}return n}},function(e,n,t){"use strict";var r=t(4);e.exports=function(e,n){var t=[][e];return!!t&&r((function(){t.call(null,n||function(){throw 1},1)}))}},function(e,n,t){var r=t(10),a=t(18),i=t(7)("match");e.exports=function(e){var n;return r(e)&&(void 0!==(n=e[i])?!!n:"RegExp"==a(e))}},function(e,n,t){"use strict";var r=t(6);e.exports=function(){var e=r(this),n="";return e.global&&(n+="g"),e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.dotAll&&(n+="s"),e.unicode&&(n+="u"),e.sticky&&(n+="y"),n}},function(e,n,t){"use strict";var r=t(20),a=t(6),i=t(4),o=t(31),s=RegExp.prototype,c=s.toString,u=i((function(){return"/a/b"!=c.call({source:"a",flags:"b"})})),l="toString"!=c.name;(u||l)&&r(RegExp.prototype,"toString",(function(){var e=a(this),n=String(e.source),t=e.flags;return"/"+n+"/"+String(void 0===t&&e instanceof RegExp&&!("flags"in s)?o.call(e):t)}),{unsafe:!0})},function(e,n,t){"use strict";var r=t(81).charAt;e.exports=function(e,n,t){return n+(t?r(e,n).length:1)}},function(e,n,t){"use strict";var r=t(24),a=t(6),i=t(35),o=t(14),s=t(22),c=t(9),u=t(33),l=t(25),f=Math.max,p=Math.min,g=Math.floor,_=/\$([$&'`]|\d\d?|<[^>]*>)/g,d=/\$([$&'`]|\d\d?)/g;r("replace",2,(function(e,n,t,r){var h=r.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,m=r.REPLACE_KEEPS_$0,v=h?"$":"$0";return[function(t,r){var a=c(this),i=null==t?void 0:t[e];return void 0!==i?i.call(t,a,r):n.call(String(a),t,r)},function(e,r){if(!h&&m||"string"==typeof r&&-1===r.indexOf(v)){var i=t(n,e,this,r);if(i.done)return i.value}var c=a(e),g=String(this),_="function"==typeof r;_||(r=String(r));var d=c.global;if(d){var S=c.unicode;c.lastIndex=0}for(var w=[];;){var E=l(c,g);if(null===E)break;if(w.push(E),!d)break;""===String(E[0])&&(c.lastIndex=u(g,o(c.lastIndex),S))}for(var P,b="",I=0,x=0;x<w.length;x++){E=w[x];for(var O=String(E[0]),C=f(p(s(E.index),g.length),0),A=[],M=1;M<E.length;M++)A.push(void 0===(P=E[M])?P:String(P));var T=E.groups;if(_){var k=[O].concat(A,C,g);void 0!==T&&k.push(T);var L=String(r.apply(void 0,k))}else L=y(O,g,C,A,T,r);C>=I&&(b+=g.slice(I,C)+L,I=C+O.length)}return b+g.slice(I)}];function y(e,t,r,a,o,s){var c=r+e.length,u=a.length,l=d;return void 0!==o&&(o=i(o),l=_),n.call(s,l,(function(n,i){var s;switch(i.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(c);case"<":s=o[i.slice(1,-1)];break;default:var l=+i;if(0===l)return n;if(l>u){var f=g(l/10);return 0===f?n:f<=u?void 0===a[f-1]?i.charAt(1):a[f-1]+i.charAt(1):n}s=a[l-1]}return void 0===s?"":s}))}}))},function(e,n,t){var r=t(9);e.exports=function(e){return Object(r(e))}},function(e,n,t){var r={};r[t(7)("toStringTag")]="z",e.exports="[object z]"===String(r)},function(e,n){e.exports=function(e,n){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:n}}},function(e,n,t){var r=t(10);e.exports=function(e,n){if(!r(e))return e;var t,a;if(n&&"function"==typeof(t=e.toString)&&!r(a=t.call(e)))return a;if("function"==typeof(t=e.valueOf)&&!r(a=t.call(e)))return a;if(!n&&"function"==typeof(t=e.toString)&&!r(a=t.call(e)))return a;throw TypeError("Can't convert object to primitive value")}},function(e,n,t){var r=t(8),a=t(4),i=t(67);e.exports=!r&&!a((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},function(e,n,t){var r=t(41),a=Function.toString;"function"!=typeof r.inspectSource&&(r.inspectSource=function(e){return a.call(e)}),e.exports=r.inspectSource},function(e,n,t){var r=t(5),a=t(28),i=r["__core-js_shared__"]||a("__core-js_shared__",{});e.exports=i},function(e,n,t){var r,a,i,o=t(68),s=t(5),c=t(10),u=t(13),l=t(11),f=t(69),p=t(46),g=s.WeakMap;if(o){var _=new g,d=_.get,h=_.has,m=_.set;r=function(e,n){return m.call(_,e,n),n},a=function(e){return d.call(_,e)||{}},i=function(e){return h.call(_,e)}}else{var v=f("state");p[v]=!0,r=function(e,n){return u(e,v,n),n},a=function(e){return l(e,v)?e[v]:{}},i=function(e){return l(e,v)}}e.exports={set:r,get:a,has:i,enforce:function(e){return i(e)?a(e):r(e,{})},getterFor:function(e){return function(n){var t;if(!c(n)||(t=a(n)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return t}}}},function(e,n,t){var r=t(44),a=t(41);(e.exports=function(e,n){return a[e]||(a[e]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.5",mode:r?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,n){e.exports=!1},function(e,n){var t=0,r=Math.random();e.exports=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++t+r).toString(36)}},function(e,n){e.exports={}},function(e,n,t){var r=t(72),a=t(5),i=function(e){return"function"==typeof e?e:void 0};e.exports=function(e,n){return arguments.length<2?i(r[e])||i(a[e]):r[e]&&r[e][n]||a[e]&&a[e][n]}},function(e,n,t){var r=t(49),a=t(51).concat("length","prototype");n.f=Object.getOwnPropertyNames||function(e){return r(e,a)}},function(e,n,t){var r=t(11),a=t(21),i=t(50).indexOf,o=t(46);e.exports=function(e,n){var t,s=a(e),c=0,u=[];for(t in s)!r(o,t)&&r(s,t)&&u.push(t);for(;n.length>c;)r(s,t=n[c++])&&(~i(u,t)||u.push(t));return u}},function(e,n,t){var r=t(21),a=t(14),i=t(73),o=function(e){return function(n,t,o){var s,c=r(n),u=a(c.length),l=i(o,u);if(e&&t!=t){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===t)return e||l||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},function(e,n){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(e,n,t){var r=t(4),a=/#|\.prototype\./,i=function(e,n){var t=s[o(e)];return t==u||t!=c&&("function"==typeof n?r(n):!!n)},o=i.normalize=function(e){return String(e).replace(a,".").toLowerCase()},s=i.data={},c=i.NATIVE="N",u=i.POLYFILL="P";e.exports=i},function(e,n,t){var r=t(8),a=t(4),i=t(11),o=Object.defineProperty,s={},c=function(e){throw e};e.exports=function(e,n){if(i(s,e))return s[e];n||(n={});var t=[][e],u=!!i(n,"ACCESSORS")&&n.ACCESSORS,l=i(n,0)?n[0]:c,f=i(n,1)?n[1]:void 0;return s[e]=!!t&&!a((function(){if(u&&!r)return!0;var e={length:-1};u?o(e,1,{enumerable:!0,get:c}):e[1]=1,t.call(e,l,f)}))}},function(e,n,t){var r=t(4);e.exports=!!Object.getOwnPropertySymbols&&!r((function(){return!String(Symbol())}))},function(e,n,t){"use strict";var r=t(4);function a(e,n){return RegExp(e,n)}n.UNSUPPORTED_Y=r((function(){var e=a("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),n.BROKEN_CARET=r((function(){var e=a("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},function(e,n,t){"use strict";var r=t(24),a=t(6),i=t(14),o=t(9),s=t(33),c=t(25);r("match",1,(function(e,n,t){return[function(n){var t=o(this),r=null==n?void 0:n[e];return void 0!==r?r.call(n,t):new RegExp(n)[e](String(t))},function(e){var r=t(n,e,this);if(r.done)return r.value;var o=a(e),u=String(this);if(!o.global)return c(o,u);var l=o.unicode;o.lastIndex=0;for(var f,p=[],g=0;null!==(f=c(o,u));){var _=String(f[0]);p[g]=_,""===_&&(o.lastIndex=s(u,i(o.lastIndex),l)),g++}return 0===g?null:p}]}))},function(e,n,t){"use strict";var r=t(24),a=t(30),i=t(6),o=t(9),s=t(82),c=t(33),u=t(14),l=t(25),f=t(23),p=t(4),g=[].push,_=Math.min,d=!p((function(){return!RegExp(4294967295,"y")}));r("split",2,(function(e,n,t){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,t){var r=String(o(this)),i=void 0===t?4294967295:t>>>0;if(0===i)return[];if(void 0===e)return[r];if(!a(e))return n.call(r,e,i);for(var s,c,u,l=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),_=0,d=new RegExp(e.source,p+"g");(s=f.call(d,r))&&!((c=d.lastIndex)>_&&(l.push(r.slice(_,s.index)),s.length>1&&s.index<r.length&&g.apply(l,s.slice(1)),u=s[0].length,_=c,l.length>=i));)d.lastIndex===s.index&&d.lastIndex++;return _===r.length?!u&&d.test("")||l.push(""):l.push(r.slice(_)),l.length>i?l.slice(0,i):l}:"0".split(void 0,0).length?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,t){var a=o(this),i=null==n?void 0:n[e];return void 0!==i?i.call(n,a,t):r.call(String(a),n,t)},function(e,a){var o=t(r,e,this,a,r!==n);if(o.done)return o.value;var f=i(e),p=String(this),g=s(f,RegExp),h=f.unicode,m=(f.ignoreCase?"i":"")+(f.multiline?"m":"")+(f.unicode?"u":"")+(d?"y":"g"),v=new g(d?f:"^(?:"+f.source+")",m),y=void 0===a?4294967295:a>>>0;if(0===y)return[];if(0===p.length)return null===l(v,p)?[p]:[];for(var S=0,w=0,E=[];w<p.length;){v.lastIndex=d?w:0;var P,b=l(v,d?p:p.slice(w));if(null===b||(P=_(u(v.lastIndex+(d?0:w)),p.length))===S)w=c(p,w,h);else{if(E.push(p.slice(S,w)),E.length===y)return E;for(var I=1;I<=b.length-1;I++)if(E.push(b[I]),E.length===y)return E;w=S=P}}return E.push(p.slice(S)),E}]}),!d)},function(e,n){e.exports=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e}},function(e,n,t){"use strict";var r=t(83).forEach,a=t(29),i=t(53),o=a("forEach"),s=i("forEach");e.exports=o&&s?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},function(e,n,t){"use strict";var r=t(12),a=t(27),i=t(21),o=t(29),s=[].join,c=a!=Object,u=o("join",",");r({target:"Array",proto:!0,forced:c||!u},{join:function(e){return s.call(i(this),void 0===e?",":e)}})},function(e,n,t){var r=t(12),a=t(35),i=t(87);r({target:"Object",stat:!0,forced:t(4)((function(){i(1)}))},{keys:function(e){return i(a(e))}})},function(e,n,t){var r=t(8),a=t(19).f,i=Function.prototype,o=i.toString,s=/^\s*function ([^ (]*)/;r&&!("name"in i)&&a(i,"name",{configurable:!0,get:function(){try{return o.call(this).match(s)[1]}catch(e){return""}}})},function(e,n,t){"use strict";var r,a=t(12),i=t(26).f,o=t(14),s=t(93),c=t(9),u=t(94),l=t(44),f="".startsWith,p=Math.min,g=u("startsWith");a({target:"String",proto:!0,forced:!!(l||g||(r=i(String.prototype,"startsWith"),!r||r.writable))&&!g},{startsWith:function(e){var n=String(c(this));s(e);var t=o(p(arguments.length>1?arguments[1]:void 0,n.length)),r=String(e);return f?f.call(n,r,t):n.slice(t,t+r.length)===r}})},function(e,n,t){"use strict";var r=t(12),a=t(50).indexOf,i=t(29),o=t(53),s=[].indexOf,c=!!s&&1/[1].indexOf(1,-0)<0,u=i("indexOf"),l=o("indexOf",{ACCESSORS:!0,1:0});r({target:"Array",proto:!0,forced:c||!u||!l},{indexOf:function(e){return c?s.apply(this,arguments)||0:a(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,n){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(e){"object"==typeof window&&(t=window)}e.exports=t},function(e,n,t){"use strict";var r={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,i=a&&!r.call({1:2},1);n.f=i?function(e){var n=a(this,e);return!!n&&n.enumerable}:r},function(e,n,t){var r=t(5),a=t(10),i=r.document,o=a(i)&&a(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,n,t){var r=t(5),a=t(40),i=r.WeakMap;e.exports="function"==typeof i&&/native code/.test(a(i))},function(e,n,t){var r=t(43),a=t(45),i=r("keys");e.exports=function(e){return i[e]||(i[e]=a(e))}},function(e,n,t){var r=t(11),a=t(71),i=t(26),o=t(19);e.exports=function(e,n){for(var t=a(n),s=o.f,c=i.f,u=0;u<t.length;u++){var l=t[u];r(e,l)||s(e,l,c(n,l))}}},function(e,n,t){var r=t(47),a=t(48),i=t(74),o=t(6);e.exports=r("Reflect","ownKeys")||function(e){var n=a.f(o(e)),t=i.f;return t?n.concat(t(e)):n}},function(e,n,t){var r=t(5);e.exports=r},function(e,n,t){var r=t(22),a=Math.max,i=Math.min;e.exports=function(e,n){var t=r(e);return t<0?a(t+n,0):i(t,n)}},function(e,n){n.f=Object.getOwnPropertySymbols},function(e,n,t){var r=t(8),a=t(5),i=t(52),o=t(76),s=t(19).f,c=t(48).f,u=t(30),l=t(31),f=t(55),p=t(20),g=t(4),_=t(42).set,d=t(80),h=t(7)("match"),m=a.RegExp,v=m.prototype,y=/a/g,S=/a/g,w=new m(y)!==y,E=f.UNSUPPORTED_Y;if(r&&i("RegExp",!w||E||g((function(){return S[h]=!1,m(y)!=y||m(S)==S||"/a/i"!=m(y,"i")})))){for(var P=function(e,n){var t,r=this instanceof P,a=u(e),i=void 0===n;if(!r&&a&&e.constructor===P&&i)return e;w?a&&!i&&(e=e.source):e instanceof P&&(i&&(n=l.call(e)),e=e.source),E&&(t=!!n&&n.indexOf("y")>-1)&&(n=n.replace(/y/g,""));var s=o(w?new m(e,n):m(e,n),r?this:v,P);return E&&t&&_(s,{sticky:t}),s},b=function(e){e in P||s(P,e,{configurable:!0,get:function(){return m[e]},set:function(n){m[e]=n}})},I=c(m),x=0;I.length>x;)b(I[x++]);v.constructor=P,P.prototype=v,p(a,"RegExp",P)}d("RegExp")},function(e,n,t){var r=t(10),a=t(77);e.exports=function(e,n,t){var i,o;return a&&"function"==typeof(i=n.constructor)&&i!==t&&r(o=i.prototype)&&o!==t.prototype&&a(e,o),e}},function(e,n,t){var r=t(6),a=t(78);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,n=!1,t={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(t,[]),n=t instanceof Array}catch(e){}return function(t,i){return r(t),a(i),n?e.call(t,i):t.__proto__=i,t}}():void 0)},function(e,n,t){var r=t(10);e.exports=function(e){if(!r(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype");return e}},function(e,n,t){var r=t(54);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(e,n,t){"use strict";var r=t(47),a=t(19),i=t(7),o=t(8),s=i("species");e.exports=function(e){var n=r(e),t=a.f;o&&n&&!n[s]&&t(n,s,{configurable:!0,get:function(){return this}})}},function(e,n,t){var r=t(22),a=t(9),i=function(e){return function(n,t){var i,o,s=String(a(n)),c=r(t),u=s.length;return c<0||c>=u?e?"":void 0:(i=s.charCodeAt(c))<55296||i>56319||c+1===u||(o=s.charCodeAt(c+1))<56320||o>57343?e?s.charAt(c):i:e?s.slice(c,c+2):o-56320+(i-55296<<10)+65536}};e.exports={codeAt:i(!1),charAt:i(!0)}},function(e,n,t){var r=t(6),a=t(58),i=t(7)("species");e.exports=function(e,n){var t,o=r(e).constructor;return void 0===o||null==(t=r(o)[i])?n:a(t)}},function(e,n,t){var r=t(84),a=t(27),i=t(35),o=t(14),s=t(85),c=[].push,u=function(e){var n=1==e,t=2==e,u=3==e,l=4==e,f=6==e,p=5==e||f;return function(g,_,d,h){for(var m,v,y=i(g),S=a(y),w=r(_,d,3),E=o(S.length),P=0,b=h||s,I=n?b(g,E):t?b(g,0):void 0;E>P;P++)if((p||P in S)&&(v=w(m=S[P],P,y),e))if(n)I[P]=v;else if(v)switch(e){case 3:return!0;case 5:return m;case 6:return P;case 2:c.call(I,m)}else if(l)return!1;return f?-1:u||l?l:I}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6)}},function(e,n,t){var r=t(58);e.exports=function(e,n,t){if(r(e),void 0===n)return e;switch(t){case 0:return function(){return e.call(n)};case 1:return function(t){return e.call(n,t)};case 2:return function(t,r){return e.call(n,t,r)};case 3:return function(t,r,a){return e.call(n,t,r,a)}}return function(){return e.apply(n,arguments)}}},function(e,n,t){var r=t(10),a=t(86),i=t(7)("species");e.exports=function(e,n){var t;return a(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!a(t.prototype)?r(t)&&null===(t=t[i])&&(t=void 0):t=void 0),new(void 0===t?Array:t)(0===n?0:n)}},function(e,n,t){var r=t(18);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,n,t){var r=t(49),a=t(51);e.exports=Object.keys||function(e){return r(e,a)}},function(e,n,t){"use strict";var r=t(36),a=t(89);e.exports=r?{}.toString:function(){return"[object "+a(this)+"]"}},function(e,n,t){var r=t(36),a=t(18),i=t(7)("toStringTag"),o="Arguments"==a(function(){return arguments}());e.exports=r?a:function(e){var n,t,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=function(e,n){try{return e[n]}catch(e){}}(n=Object(e),i))?t:o?a(n):"Object"==(r=a(n))&&"function"==typeof n.callee?"Arguments":r}},function(e,n,t){"use strict";var r=t(24),a=t(6),i=t(9),o=t(91),s=t(25);r("search",1,(function(e,n,t){return[function(n){var t=i(this),r=null==n?void 0:n[e];return void 0!==r?r.call(n,t):new RegExp(n)[e](String(t))},function(e){var r=t(n,e,this);if(r.done)return r.value;var i=a(e),c=String(this),u=i.lastIndex;o(u,0)||(i.lastIndex=0);var l=s(i,c);return o(i.lastIndex,u)||(i.lastIndex=u),null===l?-1:l.index}]}))},function(e,n){e.exports=Object.is||function(e,n){return e===n?0!==e||1/e==1/n:e!=e&&n!=n}},function(e,n){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(e,n,t){var r=t(30);e.exports=function(e){if(r(e))throw TypeError("The method doesn't accept regular expressions");return e}},function(e,n,t){var r=t(7)("match");e.exports=function(e){var n=/./;try{"/./"[e](n)}catch(t){try{return n[r]=!1,"/./"[e](n)}catch(e){}}return!1}},function(e,n,t){"use strict";function r(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){r(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}t.r(n),t.d(n,"ms",(function(){return I}));var o=t(0),s=(t(15),t(60),t(61),t(3),t(2),t(32),t(90),t(16),t(1));function c(e){if(o.a.isInApp()&&o.a.appVersion()>=7.06)return new Promise((function(n,t){s.a.exec("request",e,(function(e){n(e)}),(function(e){var n=new Error;try{n.data=JSON.parse(e)}catch(t){console.log(t),n.data=e}t(n)}))}));var n=e.url?e.url:"",t=e.method?e.method:"GET",r=e.header?e.header:{},a=e.cookies,i=e.data?e.data:{},c=e.requestDataType?e.requestDataType:"FORM",u={};if(a&&Object.keys(a).forEach((function(e){var n=e+"="+a[e]+";domain=10010.com";document.cookie=n})),"post"==t||"POST"==t){var l="";if("JSON"==c)r["Content-Type"]="application/json; charset=UTF-8",l=JSON.stringify(i);else{r["Content-Type"]="application/x-www-form-urlencoded; charset=UTF-8";var f=[];Object.keys(i).forEach((function(e){return f.push(e+"="+i[e])})),l=f.join("&")}u={body:l,cache:"no-cache",credentials:"include",headers:r,method:"post",mode:"cors"}}else{if(i){var p=[];Object.keys(e.data).forEach((function(n){return p.push(n+"="+e.data[n])})),-1===n.search(/\?/)?n+="?"+p.join("&"):n+="&"+p.join("&")}u={cache:"no-cache",credentials:"include",headers:r,method:"get",mode:"cors"}}return new Promise((function(e,t){fetch(n,u).then((function(n){"200"==n.status&&n.ok?n.text().then((function(n){e(n)})).catch((function(e){t(e)})):t(new Error(n.toString()))})).catch((function(e){t(e)}))}))}t(62);var u=[],l=[];t(63);function f(e,n){return o.a.isInApp()?new Promise((function(t,r){s.a.exec(n,e,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}))})):(console.warn("端外不支持调用"+n+"方法。"),new Promise((function(e,t){var r=new Error;r.data="端外不支持调用"+n+"方法。",t(r)})))}function p(e,n){return new Promise((function(t,r){s.a.exec(n,e,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}))}))}var g=[],_=[];var d=0;function h(){setTimeout((function(){if(o.a.isInApp()){var e=document.getElementsByTagName("html")[0].offsetHeight;if(e==d)return;d=e,s.a.exec("ListenWebViewHeight",{height:e})}}),50)}var m=Document.title;window.clientGetJsTitle=function(){return m};var v={setBounces:function(e){o.a.isIOS()&&s.a.exec("webViewAction",{bounces:e})},setTitle:function(e){m=e}};function y(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var S=function(){function e(n){!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e),this.initAttribute(n)}var n,t,r;return n=e,(t=[{key:"initAttribute",value:function(e){this.mainTitle=e.mainTitle,this.pkgName=e.pkgName,this.downloadUrl=e.downloadUrl,this.totalLength=e.totalLength,this.readableTotalLength=e.readableTotalLength,this.totalOffset=e.totalOffset,this.readableTotalOffset=e.readableTotalOffset,this.speed=e.speed,this.taskStatus=e.taskStatus,this.errorMsg=e.errorMsg,this.remark=null}},{key:"onTaskStart",value:function(){}},{key:"onTaskEnd",value:function(){}},{key:"onProgressUpdate",value:function(){}},{key:"onError",value:function(){}},{key:"startDownload",value:function(){var e=this;if(console.log("》》》开始下载",this),o.a.isInApp())s.a.exec("gameDownloadTaskExecution",{type:"startDownload",originData:this},(function(n){var t=n.data;e.initAttribute(t),"taskStart"==n.action?e.onTaskStart():"progressUpdate"==n.action?e.onProgressUpdate():"taskEnd"==n.action&&e.onTaskEnd()}),(function(n){var t=new Error;t.data=n,e.onError(t)}),!0);else{var n=new Error;n.data="端外不支持游戏分发接口调用",this.onError(n)}}},{key:"stopDownload",value:function(){var e=this;return o.a.isInApp()?(console.log("》》》停止下载",this),new Promise((function(n,t){s.a.exec("gameDownloadTaskExecution",{type:"stopDownload",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"delete",value:function(){var e=this;return o.a.isInApp()?(console.log("》》》删除下载任务和文件",this),new Promise((function(n,t){s.a.exec("gameDownloadTaskExecution",{type:"delete",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"installGame",value:function(e){var n=this;return o.a.isInApp?new Promise((function(t,r){s.a.exec("gameDownloadTaskExecution",{type:"installGame",autoDeletePkg:e,originData:n},(function(){t()}),(function(e){var n=new Error;n.data=e,r(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"openGame",value:function(){var e=this;return o.a.isInApp?new Promise((function(n,t){s.a.exec("gameDownloadTaskExecution",{type:"openGame",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"isInstalled",value:function(){return!!o.a.isInApp&&s.a.execSync("gameDownloadTaskExecution",{type:"isInstalled",originData:this})}}])&&y(n.prototype,t),r&&y(n,r),e}();t(34);var w=[],E=[];var P=t(17);function b(){var e=this;this.resolve=null,this.reject=null,this.endBlock=null,this.successData=null,this.failData=null,this.isKeep=!0,this.then=function(n){return e.resolve=n,e.successData&&setTimeout((function(){e.resolve(e.successData)}),0),e},this.catch=function(n){return e.reject=n,e.failData&&setTimeout((function(){e.reject(e.failData)}),0),e},this.finally=function(n){return e.endBlock=n,0==e.isKeep&&setTimeout((function(){e.endBlock()}),0),e}}var I=i(i({},o.a),{},{showShareMenu:P.c,updateClientMoreMenu:P.d,shareSingleChannel:P.b,openWxMiniProgram:P.a,updateEdopShareMenu:P.e,shake:function(e,n,t){if(o.a.isInApp())s.a.exec("shake",i({type:"start"},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}),!0);else{var r=new Error;r.data="端外不支持调用shake方法",t(r)}},stopShake:function(){o.a.isInApp()&&s.a.exec("shake",{type:"stop"},null,null,!0)},scanCode:function(){return o.a.isInApp()?new Promise((function(e,n){if(o.a.appVersion()>8.08)s.a.exec("scanCode",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{window.getScancodeContent=function(t){if(t){e({result:t})}else{var r=new Error;r.data={status:"11",msg:"用户主动取消了或者没有扫描到结果"},n(r)}};var t={type:"openScanCode"};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用scanCode方法。",n(t)}))},setClipboardData:function(e){o.a.isInApp()?o.a.appVersion()>8.08?s.a.execSync("setClipboardData",{data:e}):console.warn("此版本不支持调用setClipboardData方法。"):console.warn("端外不支持调用setClipboardData方法。")},getClipboardData:function(){if(o.a.isInApp()){if(o.a.appVersion()>8.08)return s.a.execSync("getClipboardData");console.warn("此版本不支持调用getClipboardData方法。")}else console.warn("端外不支持调用getClipboardData方法。")},request:c,getClientInfo:function(){return o.a.isInApp()?new Promise((function(e,n){if(o.a.appVersion()>=7.06)s.a.exec("getClientInfo",{},(function(n){n.currentPhoneNumber||(n.currentPhoneNumber=""),e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else if(u.push(e),l.push(n),window.setClientInfo=function(e){var n=JSON.parse(e);"0"==n.currentPhoneNumber&&(n.currentPhoneNumber=""),n.appVersion=n.clientVersion,n.ywCode=n.yw_code,n.provinceCode_1=n.locateProvinceCode,n.cityCode_1=n.locateCityCode,n.statusBarHeight=n.statusBar,o.a.isIOS()?(n.iosPushToken=n.deviceId,n.devicedId=n.deviceCode,n.isLogin="1"==n.isLoginOn,n.deviceBrand="iphone"==n.deviceBrand?"apple":deviceBrand):o.a.isAndroid()&&(n.devicedId=n.imei,n.huaweiPushToken=n.platformToken,n.isLogin=""!=n.currentPhoneNumber);var t=n.cookies;if(t instanceof Array)for(var r=0;r<t.length;r++){var a=t[r];if(o.a.isIOS()){if("ecs_token"==a.Name){n.ecs_token=a.Value;break}}else if(o.a.isAndroid()){if("ecs_token"==a.name){n.ecs_token=a.value;break}}}n?u.forEach((function(e){e(n)})):l.forEach((function(e){e(null)})),u=[],l=[]},o.a.isIOS()){o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify({type:"getClientInfo"})))}else o.a.isAndroid()&&window.setClientInfo(window.js_invoke.getClientInfoByJS())})):new Promise((function(e,n){var t=new Error;t.data="端外不支持getClientInfo、getClientInfoSync方法。",n(t)}))},getClientInfoSync:function(){return s.a.execSync("getClientInfo")},getSystemInfo:function(){return s.a.execSync("getSystemInfo")},getUserInfo:function(){return s.a.execSync("getUserInfo")},isLoginSync:function(){return!o.a.isInApp()||(o.a.appVersion()>=8?s.a.execSync("isLogin"):!!o.a.getCookieFromDocument("ecs_token"))},isLogin:function(){return!o.a.isInApp()||(o.a.appVersion()>=8?s.a.execSync("isLogin"):!!o.a.getCookieFromDocument("ecs_token"))},loginByClient:function(e){if(o.a.isInApp()){var n={type:"login",msg:e&&e.msg?e.msg:"",url:e&&e.url?e.url:""};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(n))}else console.warn("端外不支持调用loginByClient方法。")},getTicket:function(e){return c({url:"http://m.client.10010.com/edop_ng/getTicketByNative",method:"GET",requestDataType:"FORM",data:{token:e.ecsToken,appId:e.appId}})},navigateTo:function(e){var n=e.isNeedLogin;return!0!==n&&(n=!1),!0!==e.navigationBarHidden&&(e.navigationBarHidden=!1),o.a.isInApp()?o.a.appVersion()>=7.06?f(e,"navigateTo"):new Promise((function(n,t){var r={type:"openNewWindow",msg:{title:"",backUrl:"",isReload:"N"},url:e.target};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(r))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(r))})):new Promise((function(n,t){console.warn("端外调用navigateTo接口，只会接收target参数并简单的打开一个网页。"),e.target.startsWith("http")&&(window.location.href=e.target)}))},navigateGoBack:function(e){return o.a.isInApp()?f(e,"navigateGoBack"):new Promise((function(n,t){var r=-1;e&&e.num&&((r=parseInt(e.num))>=0&&(r=-1),Math.abs(r)>=history.length&&(r=-(history.length-1))),r<0&&window.history.go(r)}))},navigateClose:function(){if(o.a.isInApp()){var e={type:"close"};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(e))):window.js_invoke.interact(JSON.stringify(e))}else console.warn("端外不支持调用navigateClose方法。")},navigateParams:function(){return f({},"navigateParams")},navigateBackParams:function(){return f({},"navigateBackParams")},navigationBarHidden:function(){return!!(o.a.isInApp()&&o.a.appVersion()>=8.0704)&&s.a.execSync("navigationBarHidden")},setNavigationBarTitle:function(e){if(e&&(document.title=e,o.a.isInApp()))if(o.a.isIOS()){var n={type:"handJSTitle",msg:e};o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n)))}else o.a.isAndroid()&&window.js_invoke.handleJSTitle(e)},setStorage:function(e,n){var t={key:e,value:n};return o.a.isInApp()&&o.a.appVersion()>7.06?p(t,"setStorage"):new Promise((function(n){window.localStorage.setItem(e,JSON.stringify(t)),n({status:"success",key:e,msg:""})}))},setStorageSync:function(e,n){var t={key:e,value:n};return o.a.isInApp()&&o.a.appVersion()>7.06?s.a.execSync("setStorage",t):(window.localStorage.setItem(e,JSON.stringify(t)),{status:"success",key:e,msg:""})},getStorage:function(e){return o.a.isInApp()&&o.a.appVersion()>7.06?p({key:e},"getStorage"):new Promise((function(n){var t=window.localStorage.getItem(e),r=JSON.parse(t);n(r?r.value:"")}))},getStorageSync:function(e){if(o.a.isInApp()&&o.a.appVersion()>7.06){var n={key:e};return s.a.execSync("getStorage",n)}var t=window.localStorage.getItem(e),r=JSON.parse(t);return r&&r.value?r.value:""},deleteStorage:function(e){return o.a.isInApp()&&o.a.appVersion()>7.06?p({key:e},"deleteStorage"):new Promise((function(n){window.localStorage.removeItem(e),n({status:"success",key:e,msg:""})}))},deleteStorageSync:function(e){if(o.a.isInApp()&&o.a.appVersion()>7.06){var n={key:e};return s.a.execSync("deleteStorage",n)}return window.localStorage.removeItem(e),{status:"success",key:e,msg:""}},clearStorage:function(){if(o.a.isInApp()&&o.a.appVersion()>8.08){return p({},"clearStorage")}return new Promise((function(e,n){var t=new Error;t.data="不支持clearStorage方法。",n(t)}))},clearStorageSync:function(){if(o.a.isInApp()&&o.a.appVersion()>8.08){return s.a.execSync("clearStorage",{})}return{status:"fail",msg:"不支持clearStorageSync"}},getStorageInfo:function(){if(o.a.isInApp()&&o.a.appVersion()>8.08){return p({},"getStorageInfo")}return new Promise((function(e,n){var t=new Error;t.data="不支持getStorageInfo方法。",n(t)}))},getStorageInfoSync:function(){if(o.a.isInApp()&&o.a.appVersion()>8.08){return s.a.execSync("getStorageInfo",{})}return{status:"fail",msg:"不支持getStorageInfoSync"}},chooseContact:function(){return o.a.isInApp()?new Promise((function(e,n){if(o.a.appVersion()>8.0705)s.a.exec("chooseContact",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{window.clientSetContactPhoneNum=function(t){if(t){var r=JSON.parse(t);if(r&&r.telinfo&&Array.isArray(r.telinfo)&&r.telinfo.length>0){var a=[],i={phoneNumber:"",displayName:"",phoneNumberList:a};return r.telinfo.forEach((function(e){var n=e.name,t=e.phone;i.displayName=n,a.push(t)})),i.phoneNumber=a[0],i.phoneNumberList=a,void e(i)}}var o=new Error;o.data={status:"10",msg:"没有联系人信息，请升级APP新版本"},n(o)};var t={type:"openContact"};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用chooseContact方法。",n(t)}))},searchContacts:function(e){if(o.a.isInApp()){var n={phoneNumber:e};return new Promise((function(e,t){if(o.a.appVersion()>8.0705)s.a.exec("searchContacts",n,(function(n){e(n)}),(function(e){var n=new Error;n.data=e,t(n)}));else{var r=new Error;r.data="当前版本不支持searchContacts方法。",t(r)}}))}return new Promise((function(e,n){var t=new Error;t.data="端外不支持调用searchContacts方法。",n(t)}))},voiceRecognitionStart:function(e,n){if(!(o.a.isInApp()&&o.a.appVersion()>=7.06))return new Promise((function(e,n){var t=new Error;t.data="端外或者低版本不支持调用voiceRecognitionStart方法",n(t)}));s.a.exec("voiceRecognition",{type:"start"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}),!0)},voiceRecognitionEnd:function(){s.a.exec("voiceRecognition",{type:"end"})},voiceRecognitionCancel:function(){s.a.exec("voiceRecognition",{type:"cancel"})},getLocationInfo:function(){return o.a.isInApp()?new Promise((function(e,n){if(o.a.appVersion()>=7.06)s.a.exec("getLocationInfo",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{g.push(e),_.push(n);var t={callbackFromNative:function(e,n){if("lbsData"==e){var t=JSON.parse(n);if(t&&1==t.status&&t.latitude&&t.longitude)t.provinceName=t.province,t.cityName=t.city,g.forEach((function(e){e(t)}));else{var r={};t&&12==t.status?(r.status=12,r.msg="用户没有开启APP的定位权限"):(r.status=10,r.msg="程序异常");var a=new Error;a.data=r,_.forEach((function(e){e(a)}))}}g=[],_=[]}};if(window.bridge=t,o.a.isIOS()){o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify({type:"getGeoLocation"})))}else o.a.isAndroid()&&window.js_invoke.getGeoLocation()}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用getLocationInfo方法。",n(t)}))},onAppShow:function(e){s.a.addEventListener("onPageShow",e)},onAppHide:function(e){s.a.addEventListener("onPageHidden",e)},onPageHidden:function(e){s.a.addEventListener("onPageHidden",e)},onPageShow:function(e){s.a.addEventListener("onPageShow",e)},onPageScrollToEnd:function(e,n){o.a.isInApp()&&!n?s.a.addEventListener("pageScrollToEnd",e):window.addEventListener("scroll",(function(){Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)-function(){var e=0;document.documentElement&&document.documentElement.scrollTop?e=document.documentElement.scrollTop:document.body&&(e=document.body.scrollTop);return e}()-function(){var e=0;e=document.body.clientHeight&&document.documentElement.clientHeight?Math.min(document.body.clientHeight,document.documentElement.clientHeight):Math.max(document.body.clientHeight,document.documentElement.clientHeight);return e}()<20&&e()}))},onNetworkStatusChanged:function(e){s.a.addEventListener("onNetworkStatusChanged",e)},openSystemSetting:function(e){"string"!=typeof e&&(e=""),s.a.exec("openSystemSetting",{type:e})},webView:v,getAllNavMenu:function(){return o.a.isInApp()?new Promise((function(e,n){s.a.exec("clientNavMenu",{type:"getAllNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getAllNavMenu方法。",n(t)}))},getChangYongNavMenu:function(){return o.a.isInApp()?new Promise((function(e,n){s.a.exec("clientNavMenu",{type:"getChangYongNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getChangYongNavMenu方法。",n(t)}))},getHomeCustomNavMenu:function(){return o.a.isInApp()?new Promise((function(e,n){s.a.exec("clientNavMenu",{type:"getHomeCustomNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getHomeCustomNavMenu方法。",n(t)}))},updateHomeCustomNavMenu:function(e){return o.a.isInApp()?new Promise((function(n,t){s.a.exec("clientNavMenu",{type:"updateHomeCustomNavMenu",value:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=JSON.parse(e),t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持updateHomeCustomNavMenu方法。",n(t)}))},getLanguageJson:function(){return o.a.isInApp()?new Promise((function(e,n){s.a.exec("clientNavMenu",{type:"getLanguageJson"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getLanguageJson方法。",n(t)}))},showMsg:function(e){if(o.a.isInApp())return new Promise((function(n,t){s.a.exec("alert",e,(function(){n()}),(function(){t()}))}));alert("端外模拟弹窗："+e.msg)},toast:function(e,n){n||(n=2e3),e||(e="");var t={msg:e,time:n};if(o.a.isInApp())return new Promise((function(n,r){e?s.a.exec("toast",t,(function(e){n(e)}),(function(e){var n=new Error;n.data=JSON.parse(e),r(n)})):r()}));alert("端外模拟Toast："+e)},sentBodyHeightToNative:function(){window.sentBodyHeightToNativeOn=!0;var e=document.getElementsByTagName("html")[0];new MutationObserver(h).observe(e,{attributes:!0,childList:!0,subtree:!0}),h()},getGameDownloadTask:function(e){if(o.a.isInApp()){var n=s.a.execSync("getGameDownloadTask",e),t=new S(n);return"RUNNING"==t.taskStatus&&t.startDownload(),t}var r=new Error;return r.data="端外不支持游戏分发接口调用",r},GameDownloadTask:S,getNetworkType:function(){return function(e){var n=e.mnc,t="";switch(n){case 0:t="ChinaMobile";break;case 1:t="ChinaUnicom";break;case 2:t="ChinaMobile";break;case 3:t="ChinaTelecom";break;case 4:t="ChinaMobile";break;case 5:t="ChinaTelecom";break;case 6:t="ChinaUnicom";break;case 7:case 8:t="ChinaMobile";break;case 9:case 10:t="ChinaUnicom";break;case 11:case 12:t="ChinaTelecom";break;case 13:t="ChinaMobile";break;default:t=""}return e.carrier=t,e}(s.a.execSync("getNetworkType"))},ping:function(e){if(!o.a.isInApp())return new Promise((function(e,n){var t=new Error;t.data="端外暂不支持ping方法。",n(t)}));s.a.exec("ping",{host:e},(function(e){resolve(e)}),(function(e){var n=new Error;n.data=e,reject(n)}))},checkSystemPermission:function(e){return o.a.isInApp()?new Promise((function(n,t){s.a.exec("systemPermission",{type:"checkSystemPermission",permission:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持checkSystemPermission方法。",n(t)}))},chooseImage:function(e){return o.a.isInApp()?new Promise((function(n,t){if(o.a.appVersion()>8.0703)s.a.exec("chooseImage",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}));else{w.push(n),E.push(t),window.callbackWithImage=function(e){if(e){var n={imageType:"jpg",imageData:e};w.forEach((function(e){e(n)}))}else{var t=new Error;t.data={status:"10",msg:"程序异常"},E.forEach((function(e){e(t)}))}w=[],E=[]};var r="0";e&&"camera"==e.sourceType?r="1":e&&"album"==e.sourceType&&(r="2");var a={type:"takePhotoOrCamera",msg:r};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(a))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(a))}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用chooseImage方法。",n(t)}))},saveImageToPhotosAlbum:function(e){if(o.a.isInApp()){if(e&&e.base64Data){var n=e.base64Data,t={type:"saveBase64Image",msg:n=n.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))}}else console.warn("端外不支持调用saveImageToPhotosAlbum方法。")},handSignature:function(){return o.a.isInApp()?new Promise((function(e,n){window.setSignPicture=function(t){if(t&&"0"==t.code){var r=t.data;e(r)}else{var a=new Error;a.data="没有正常生成手写签名数据",n(a)}};var t={type:"getSignPicture"};o.a.isIOS()?o.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):o.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用handSignature方法。",n(t)}))},exec:function(e,n){if(o.a.isInApp())return new Promise((function(t,r){s.a.exec(e,n,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}),!1)}));alert("端外不支持方法："+e)},execSync:function(e,n){return o.a.isInApp()?s.a.execSync(e,n):(alert("端外不支持方法："+e),null)},execKeep:function(e,n){var t=new b;return s.a.exec(e,n,(function(e,n){t.successData=e||"",t.resolve&&t.resolve(e),0==n&&t.endBlock&&t.endBlock()}),(function(e,n){var r=new Error;r.data=e,t.failData=r,t.reject&&t.reject(r),0==n&&t.endBlock&&t.endBlock()}),!0),t},execKeepSendMsg:function(e,n){s.a.exec(e,n,null,null,!0)},pageFontModel:function(){if(o.a.appVersion()>=8.03){var e=s.a.execSync("fontSizeModel");if("string"==typeof e)return e}return"0"},getLanguageInfo:function(){return o.a.isInApp()&&o.a.appVersion()>=8.0702?s.a.execSync("getLanguageInfo"):"chinese"},addEventListener:function(e,n){s.a.addEventListener(e,n)}});window.ms=I}]);