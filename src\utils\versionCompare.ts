/**
 * 比较数组数字大小
 * @param arr1
 * @param arr2
 * @returns {Number}
 */
const compare = (arr1: string[], arr2: string[]): number => {
  let ret = -1 // 0-数组2大，1-数组1大，2-相等
  const compareLength = Math.max(arr1.length, arr2.length)
  for (let index = 0; index < compareLength; index++) {
    if (arr1[index] && arr2[index]) {
      // 比较大小
      if (arr1[index] > arr2[index]) {
        ret = 1
      } else if (arr1[index] === arr2[index]) {
        ret = 2
      } else {
        ret = 0
      }
    } else if (arr1[index]) {
      // 仅数组1存在
      ret = 1
    } else if (arr2[index]) {
      // 仅数组2存在
      ret = 0
    }
    if (ret === 0 || ret === 1) return ret
  }
  return ret
}

/**
 * 版本号比较
 * @param ver1
 * @param ver2
 * @returns {Number} 0-第二个大，1-第一个大，2-相等，-1-版本号存在错误
 */
export const versionCompare = (ver1: string, ver2: string): number => {
  if (ver1 && ver2) {
    const ver1Arr = ver1.split('.')
    const ver2Arr = ver2.split('.')
    return compare(ver1Arr, ver2Arr)
  }
  return -1
}
