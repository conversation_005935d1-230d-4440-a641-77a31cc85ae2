// 文档 http://ecstest2018.10010.com/html/android/unicomjs/index.html
import { isIos, isAndroid, isUnicom } from '../env'
import { log } from '../_tools/log'
import { IUnicomClientInfo } from '../types'

const trim = (str: string) => {
  return str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '')
}

/**
 * 获取客户端设备信息
 * @param callback
 */
export const unicomGetClientInfo = (callback: Function) => {
  if (isUnicom) {
    window.setClientInfo = (info: string) => {
      log('UnicomJsBrige-客户端返回信息：' + info)
      if (typeof callback === 'function') {
        const infoOBJ: IUnicomClientInfo = JSON.parse(info)
        const currentPhoneNumber = infoOBJ.currentPhoneNumber
        if (currentPhoneNumber === null || currentPhoneNumber === undefined) {
          infoOBJ.currentPhoneNumber = ''
        } else if (currentPhoneNumber === '0') {
          infoOBJ.currentPhoneNumber = ''
        } else {
          infoOBJ.currentPhoneNumber = trim(currentPhoneNumber)
        }
        callback(infoOBJ)
      }
    }
    if (isIos) {
      const config = { type: 'getClientInfo' }
      window.location.href = encodeURI('clientAction=' + JSON.stringify(config))
    } else if (isAndroid) {
      window.setClientInfo(window.js_invoke.getClientInfoByJS())
    }
  } else {
    log('UnicomJsBrige-当前不在联通手厅客户端内部,无法调用getClientInfo')
  }
}

// 返回的 info 例子
// const infoDemo = {
//   cookies: [
//     {
//       Domain: '.10010.com',
//       Name: 'ecs_token',
//       Value: 'eyJkYXRhIjoiNDIwMTcxYzk1YWY4NWM1OTdiOWNkNjEzNTkxNDY0ZTk4MmViOTgzY2JiZjMzNTg3MDA2NjkyMGE4ZGJhOWI1MDg5NTAzYjIxM2E0N2EyM2YzMjU4YWVlOTRhY2FlODk4NDllMzVjMDZlYTI2YTgxMDRmYjg2NWZjZjNiNjlkMzIyMTUzNDk4NDgzYjRjYjc5YjU1NThiNDFjYzJmMmE2ODcxYjMzZDIwYzQ1N2NkZmZkODA1NGU0MDk5OTY1NzEzIiwidmVyc2lvbiI6IjAwIn0=',
//       Path: '/'
//     },
//     {
//       Domain: '.10010.com',
//       Name: 'u_account',
//       Value: '***********',
//       Path: '/'
//     }
//   ],
//   currentPhoneNumber: '***********',
//   imei: '***************',
//   clientVersion: 'android@7.0401',
//   osVersion: 'android10',
//   osVersionCode: '29',
//   pip: '*************',
//   netWay: '4G',
//   deviceBrand: 'HUAWEI',
//   deviceModel: 'HMA-AL00',
//   statusBar: '81',
//   statusBarHeight: '56',
//   yw_code: '',
//   platformToken: 'AKUAW78ZMKN5Cs304FyXLg6zrL68OrdiuIcaZrEKXImjL4oLSqpbt4MkVtCrqvMsygIaOgrixGSmqG6PDFB_rHQAYdX1nZkq2-uJylXIS3GGVoHQ34V-z08uDP0cLpjUtA',
//   provinceCode: '011',
//   cityCode: '110',
//   locateProvinceCode: '011',
//   locateCityCode: '110',
//   locateCityName: '北京'
// }

/**
 * 获取当前登录的手机号码
 * 如果没有登录返回空字符串，如果登录了返回实际的电话号码
 * @param callback
 */
export const unicomGetCurrentPhone = (callback: Function) => {
  unicomGetClientInfo((info: IUnicomClientInfo) => {
    callback(info.currentPhoneNumber)
  })
}

/**
 * 拉起登录
 * @param retainWebview 是否保留webview
 */
export const unicomLoginByClient = (retainWebview: boolean) => {
  if (isUnicom) {
    if (isIos) {
      const config = {
        type: 'serviceLogin',
        msg: retainWebview ? 'retainWebview' : ''
      }
      window.location.href = encodeURI('clientAction=' + JSON.stringify(config))
    } else if (isAndroid) {
      const config = {
        type: 'login',
        msg: retainWebview ? 'retainWebview' : ''
      }
      window.js_invoke.interact(JSON.stringify(config))
    }
  }
}

/**
 * 关闭webview
 */
export const unicomCloseWeb = () => {
  if (isUnicom) {
    const config = { type: 'close' }
    if (isIos) {
      window.location.href = encodeURI('clientAction=' + JSON.stringify(config))
    } else if (isAndroid) {
      window.js_invoke.interact(JSON.stringify(config))
    }
  }
}

/**
 * 设置客户端标题栏标题
 * @param title
 */
export const unicomSetTitle = (title: string) => {
  if (isUnicom) {
    if (isIos) {
      const titleConfig = { type: 'handJSTitle', msg: title }
      window.location.href = encodeURI('clientAction=' + JSON.stringify(titleConfig))
    } else if (isAndroid) {
      window.js_invoke.handleJSTitle(title)
    }
  }
}

/**
 * 分享
 * @param shareTitle
 * @param shareContent
 * @param shareURL
 * @param shareIconURL
 */
export const unicomShare = (shareTitle: string, shareContent: string, shareURL: string, shareIconURL: string) => {
  const shareConfig = {
    type: 'share2',
    url: '',
    shareList: 'wechat,wechatmoments,qq,qzone,sinaweibo,email,shortmessage',
    msg: '',
    shareJson: {
      shareType: 'url',
      shareTitle,
      shareContent,
      shareURL,
      shareIconURL
    }
  }
  if (isIos) {
    window.location.href = 'clientAction=' + JSON.stringify(shareConfig)
  } else if (isAndroid) {
    window.js_invoke.interact(JSON.stringify(shareConfig))
  }
}

/**
 * 获取地理位置定位
 * @param callback
 */
export const unicomGetGeoLocation = (callback: Function) => {
  // 注册回调函数，接收地理位置信息
  window.bridge = {
    callbackFromNative: (flag: string, info: any) => {
      log('UnicomJsBrige-获取地理位置信息：' + flag + ' ' + info)
      if (typeof callback === 'function') {
        const infoObj = JSON.parse(info)
        callback(infoObj)
      }
    }
  }

  if (isUnicom) {
    const config = { type: 'getGeoLocation' }
    if (isIos) {
      window.location.href = encodeURI('clientAction=' + JSON.stringify(config))
    } else if (isAndroid) {
      window.js_invoke.getGeoLocation()
    }
  }
}

/**
 * 拨打电话，如果phoneNumber是加密过的，isEncry要传true
 * @param {String} phoneNumber
 * @param {Boolean} isEncry
 */
export const unicomTel = (phoneNumber: string, isEncry: boolean) => {
  if (isUnicom) {
    let config
    if (isEncry) {
      config = {
        type: 'teljiami',
        msg: {
          epLinkTelphone: phoneNumber // 加密手机号
        }
      }
    } else {
      config = {
        type: 'tel',
        url: phoneNumber // 手机号
      }
    }
    if (isIos) {
      window.location.href = encodeURI('clientAction=' + JSON.stringify(config))
    } else if (isAndroid) {
      window.js_invoke.interact(JSON.stringify(config))
    }
  }
}
