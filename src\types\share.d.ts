declare global {
  interface Window {
    wx: any
    js_invoke: any
  }
}

export interface IShareApiData {
  signature: string
  jsapi_ticket: string
  nonceStr: string
  timestamp: string
}

export interface IShareOptionsLite {
  link?: string
  title?: string
  describe?: string
  picUrl?: string
  channel?: string[]
}

export interface IShareOptions extends IShareOptionsLite {
  callback?: ICallbackFunc // 分享或操作完成后的结果
  next?: INextFunc // 当前渠道不支持分享，需要手工后续处理
}

export interface INextShareOptions extends IShareOptionsLite {
  type: 'h5' | 'weixin'
}

interface INextFunc {
  (data: INextShareOptions, userData: any): void
}

interface ICallbackFunc {
  (obj: ICallbackData, userData: any): void
}

interface ICallbackData {
  status: boolean
  type: 'wopay' | 'h5' | 'weixin'
  errCode: number
  // 0-默认，无错误
  // 10-沃钱包不明原因错误
  // 20-手厅错误
  // 30-微信分享获取params接口失败
  // 31-微信app内聊天分享失败
  // 32-微信朋友圈初始化失败
  // 33-微信app内聊天/朋友圈初始化失败
  // 34-微信 wx.checkJsApi 失败
  // 35-微信 wx.error 失败
}
