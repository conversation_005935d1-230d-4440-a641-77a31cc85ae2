import { log } from '../_tools/log'
import { ENV_APP_NAME } from '../_tools/envAppName'
import { storage } from '../utils'

const BASE = window.location.origin // like: https://epay.10010.com
const basePath = (path?: string) => (path && path.indexOf('http') === 0) ? path : BASE + path

// 沃钱包判断是否登录、登录一体化接口
export const WQB_H5_API_URL = (path?: string) => basePath(path) + '/mzLoginAuth'

// 调用沃钱包客户端获取rpt时候，需要上送我方app已经开通的clientId（生产及测试环境相同）
const WOPAY_CLIENT_ID_MAP = {
  DEFAULT: 'fa6ced63-b84a-46c0-9e45-d9171dee3f9f', // 默认
  'ITF-FI-CORE-WEB': 'fa6ced63-b84a-46c0-9e45-d9171dee3f9f', // 理财
  'ITF-CL-CORE-WEB': 'b8e2e8b9-1781-49e6-b111-899a546be2bc', // 易贷
  'PS-CCMS-BIZ-WEB': 'a4c07010-f246-4ee9-9824-19c4dbcef6b3', // 渠道商城
  'IS-ESPS-DJ-WEB': 'ce981edd-58cb-4901-bd5c-622817481d8d', // 党费
  'IS-UPS-SERVICE-WEB': '39c43402-cdd5-40ba-8f65-76a0c98aca2c', // 工会费
  'IS-CYPS-SERVICE-WEB': '68255ed6-a2b4-4329-b9c8-9c8af120281a', // 团费
  'CV-CCR-REPAY-WEB': 'b42961d4-94a5-48ab-b28d-8cf67f495e16', // 信用卡还款
  'CL-PUPS-SERVICE-WEB': '3b94fd4a-80bf-49cb-8376-988ed95f367f' // 生活缴费
}

// 默认导出
export const WOPAY_CLIENT_ID = () => {
  const envAppName: string = ENV_APP_NAME()
  log('当前环境APP', envAppName)
  if (window.COMMONKIT_WOPAY_CLIENT_ID) return window.COMMONKIT_WOPAY_CLIENT_ID
  // @ts-ignore
  if (WOPAY_CLIENT_ID_MAP[envAppName]) return WOPAY_CLIENT_ID_MAP[envAppName]
  else return WOPAY_CLIENT_ID_MAP.DEFAULT
}

// 登录参数缓存名（内部用）
export const loginParamsStorage = storage('COMMONKIT_LOGIN_PARAMS')
// 登录参数缓存名（外部用）
export const loginParamsStorageForUser = storage('COMMONKIT_LOGIN_USER_PARAMS')

// 用户号
export const loginCifId = storage('COMMONKIT_LOGIN_CIF_ID')
// 用户号持久缓存（仅用于日志上报使用，不能用于逻辑判断）
export const loginCifIdCache = storage('COMMONKIT_LOGIN_CIF_ID_L', true)

// 政企商户信息
export const loginZqInfo = storage('COMMONKIT_LOGIN_ZQ_INFO')

// 沃钱包当前线程缓存（用于判断当前线程是否已登录过，已登录直接走接口登录，不再获取rptid）
export const wopayLoginStateCache = storage('COMMONKIT_WOPAY_LOGIN_STATE')

// 手厅手机号缓存名（用于确认是否切换过手机号使用）
export const unicomLoginPhoneNoStorage = storage('COMMONKIT_UNICOM_LOGIN_PHONE_NO', true)

// 登录调用 API 时长
export const loginTimer = { apiTime: 0, sdkTime: 0 }

// 手厅、H5调用登录跳转返回标志
export const loginH5JumpData = storage('COMMONKIT_UNICOM_H5_FLAG')
