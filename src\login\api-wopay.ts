import { WopayClient<PERSON>pi } from '../WopayClientApi'
import { formPostLogin } from '../_tools/request'
import { woReport } from '../_tools/reporter'
import { log } from '../_tools/log'
import { getLoginParams } from './utils'
import {
  WOPAY_CLIENT_ID,
  WQB_H5_API_URL,
  wopayLoginStateCache,
  loginTimer,
  loginCifId,
  loginCifIdCache,
  loginZqInfo
} from './config'
import { WopayLoginType, WopayRptidSourceForm } from './enum'
import {
  ILoginH5Resp,
  ILoginH5RespWrapped,
  ILoginInnerResp,
  ILoginOptions,
  ILoginParams,
  ILoginWopayRptResp
} from '../types'

/**
 * 新版沃钱包登录验证
 * @param opts
 * @returns {Promise<any>}
 */
export const wqbNewAppStatus = (opts: ILoginOptions): Promise<any> => new Promise<ILoginInnerResp>((resolve) => {
  const { projectPath, wopayLoginMode, wopayRptTimeout } = opts
  log('login-wqbStatus 请求获取APP登录信息')
  const clientId = WOPAY_CLIENT_ID()
  log('login-wqbStatus 使用clientId', clientId)

  // 统计调用 sdk 获取 rptid 时间
  const d1 = new Date().getTime()
  let sdkRptidTimeCount = 0
  let getRptIdTimeout = false // 当前是否获取完成 rptid （不论是否成功）标志，已获取则不进行超时上报
  let timeoutFlag = false // 当前超时后是否完成上报标志，上报后如果后续获取成功，再上报一次反向日志

  const apiFn = (rptid: string, rptidFrom: number, next: Function) => {
    // 存在登录信息，与后端验证是否有效
    const postData: ILoginParams = {
      source: 'app_wqb',
      rptid,
      updateRptid: (wopayLoginMode === WopayLoginType.NEED_ONCE && rptid) ? '1' : '2', // 1-让后端清理 session，并用新的，2-(默认)后端如果有老的 session，可以不读取新的 rptid
      extraTxt: `SRTC=${sdkRptidTimeCount.toString()}ms;`
    }
    loginTimer.sdkTime = sdkRptidTimeCount

    const t1 = new Date()
    formPostLogin(WQB_H5_API_URL(projectPath), postData, { 'WOAUTH-AJAX-REQUEST': true }, {}, ([err, json]) => {
      const t2 = new Date()
      loginTimer.apiTime = t2.getTime() - t1.getTime()
      if (err) {
        log('login-wqbStatus 同步沃钱包登录信息失败')
        woReport('login-wqbStatus 同步沃钱包登录信息失败', postData, err, 'rptid来源', rptidFrom)
        wopayLoginStateCache.set('0')
        next && next({
          status: false,
          data: {
            redirectUrl: '',
            cifId: '',
            zqInfo: [],
            err: '[COMMONKIT] wopay-login: sync login info fail.',
            errId: 204,
            netErr: true
          }
        })
        return
      }
      log('login-wqbStatus', json)
      const result: ILoginH5Resp = (json as ILoginH5RespWrapped).data ? (json as ILoginH5RespWrapped).data : (json as ILoginH5Resp)
      if (result.needRedirect === 'false') {
        // 已登录
        wopayLoginStateCache.set('1')
        loginCifId.set(result.cifId)
        loginCifIdCache.set(result.cifId)
        loginZqInfo.set(result.g2bLoginInfo)
        next && next({
          status: true,
          data: {
            redirectUrl: result.redirectUrl,
            cifId: result.cifId,
            zqInfo: result.g2bLoginInfo,
            err: '',
            errId: 0,
            netErr: false
          }
        })
      } else {
        // 未登录
        if (!result.redirectUrl) {
          log('login-wqbStatus 通过 rptid 登录失败')
          woReport('login-wqbStatus 通过 rptid 登录失败', postData, result)
        }
        wopayLoginStateCache.set('0')
        next && next({
          status: false,
          data: {
            redirectUrl: result.redirectUrl,
            cifId: '',
            zqInfo: [],
            err: '[COMMONKIT] wopay-login: login failed via rptid.',
            errId: 203,
            netErr: false
          }
        })
      }
    })
  }

  const rptFn = ({ onlyUseSDK = true }) => {
    log('login-wqbStatus rpt获取流程，当前必须使用 sdk 获取：', onlyUseSDK)
    setTimeout(() => {
      if (!getRptIdTimeout) {
        log('login-wqbStatus 通过sdk获取rptId超时，但数据仍在等待中...')
        woReport('login-wqbStatus 通过sdk获取rptId超时')
        timeoutFlag = true
      }
    }, (wopayRptTimeout || 3) * 1000)

    const { rptid } = getLoginParams(true)
    if (rptid && !onlyUseSDK) {
      getRptIdTimeout = true
      // 沃钱包客户端，可以配置带 rptid 跳转业务（跳转时字段名是 rpt，在 getLoginParams 已兼容）
      apiFn(rptid, WopayRptidSourceForm.URL, (resolveData: ILoginInnerResp) => {
        if (!resolveData.status) {
          rptFn({ onlyUseSDK: true })
        } else {
          resolve(resolveData)
        }
      })
    } else {
      WopayClientApi.exRpt({
        isUpdate: !!clientId,
        code: clientId,
        success: function (json: ILoginWopayRptResp) {
          const d2 = new Date().getTime()
          sdkRptidTimeCount = d2 - d1
          log('login-wqbStatus 获取APP信息成功', json)
          // 当前是超时后成功的，再上报一次告知取消记录
          if (timeoutFlag) {
            woReport(`login-wqbStatus 通过sdk获取rptId超时，现已成功-${sdkRptidTimeCount}ms`)
          }
          const rptid = json.rpt
          if (!rptid) {
            // 失败
            log('login-wqbStatus 无效或不存在有效rpt值，未登录')
            resolve({
              status: false,
              data: {
                redirectUrl: '',
                cifId: '',
                zqInfo: [],
                err: '[COMMONKIT] wopay-login: rptid is invalid or empty',
                errId: 202,
                netErr: false
              }
            })
            return
          }
          getRptIdTimeout = true
          apiFn(rptid, WopayRptidSourceForm.SDK, (resolveData: ILoginInnerResp) => {
            resolve(resolveData)
          })
        },
        failed: function (e: any) {
          log('login-wqbStatus 获取沃钱包rptid登录信息失败')
          woReport('login-wqbStatus 获取沃钱包rptid登录信息失败', e)
          getRptIdTimeout = true
          // 用户未登录，会直接进入failed流程，导致失败，此时认定为未登录即可
          resolve({
            status: false,
            data: {
              redirectUrl: '',
              cifId: '',
              zqInfo: [],
              err: '[COMMONKIT] wopay-login: get rptid fail.',
              errId: 205,
              netErr: true
            }
          })
        }
      })
    }
  }

  if (wopayLoginStateCache.get() === '1') {
    // 当前线程已登录过，直接与后端通信
    log('login-wqbStatus 当前线程已登录')
    apiFn('', WopayRptidSourceForm.DEFAULT, (resolveData: ILoginInnerResp) => {
      if (resolveData.status) {
        // 已正常登录，不用处理
        log('login-wqbStatus 当前线程已登录，正常')
        resolve(resolveData)
      } else {
        // 登录失败，应该是超时导致，重新走 rptid 获取流程
        log('login-wqbStatus 当前线程已登录，接口返回未登录，重新 SDK 登录')
        rptFn({ onlyUseSDK: false })
      }
    })
  } else {
    // 当前线程没登录过，需要走 rptid 获取流程，获取后再与后端通信
    log('login-wqbStatus 当前线程未登录')
    rptFn({ onlyUseSDK: false })
  }
})

/**
 * 新版沃钱包APP登录
 * @param opts
 */
export const wqbNewAppLogin = (opts: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  function check (_resolve: any) {
    wqbNewAppStatus(opts).then(json => {
      if (json.status) {
        log('login-wqbLogin 已登录')
        // 已登录
        _resolve(json)
      } else {
        // 未登录
        log('login-wqbLogin 未登录')
        login(_resolve)
      }
    })
  }

  function login (_resolve: any) {
    WopayClientApi.login({
      success: function () {
        log('login-wqbLogin 登录成功')
        // 登录后回调
        check(_resolve)
      }
    })
  }

  check(resolve)
})
