export {
  isAndroid, androidVer,
  isIos, iOSVer,
  isHarmonyOS, harmonyOSVer,
  isAlipay, alipayVer,
  isQQ, isTim, isQQInternational, qqVer,
  isUnicom, unicomVer,
  isWeixin, isWeixinWork, isWeixinLike, weixinVer,
  isWopay, wopayVer
} from './env'

export {
  LoginComponent,
  status, login,
  useLogin, loginRun, loginCallback,
  beforeEachLoginInterceptor,
  loginTimer,
  WopayLoginType, RptidUpdateType, LoginType,
  getUrlLoginParams,
  getCifId, getZqInfo,
  loginJumpHook
} from './login'

export { risk } from './risk'

export { shareInit, setWeiXinShareData, share } from './shareAll'

export { urlParse, urlAppend, getUrlParams, qsParse, qsStringify, customUrlEncoder } from './url'

export { zhugeInit, zhugeTrack, zhugeAppendDefaultData } from './zhugeTrack'

export { analyticsRun } from './analytics'

export {
  versionCompare, preload, storage,
  jsonPost, formGet, formPost, formSubmit, cached, GroupSend, axios,
  woReport,
  log, warn, error, debug
} from './utils'

export {
  ms,
  loadMs,
  msFaceHelper,
  unicomCloseWeb,
  unicomShare,
  unicomTel,
  unicomGetClientInfo,
  unicomLoginByClient,
  unicomGetCurrentPhone,
  unicomGetGeoLocation,
  unicomSetTitle
} from './UnicomClientApi'

export {
  WopayClientApi,
  wopayClosePage, wopayOpenPage, wopayDeviceInfo, wopayLocationInfo, wopayRightButton
} from './WopayClientApi'

export { vueRouterFix, afterEachScroll, afterEachSetTitle } from './VueHelper'
