export interface INavigationPerfData {
  // 重定向时间
  redirectTime: number
  // DNS 查询时间
  domainLookupTime: number
  // TCP 连接时间
  connectTime: number
  // 请求响应时间
  requestTime: number
  // DOM 解析时间
  domContentLoadedTime: number
  // 加载事件时间
  loadEventTime: number
  // 页面总加载时间
  durationTime: number
}

export interface INavigationPerf {
  (): INavigationPerfData
}

export interface IPaintPerfData {
  // FP
  fpTime: number
  // FCP
  fcpTime: number
}

export interface IPaintPerf {
  (callback: (info: IPaintPerfData) => void): void
}

export interface ILcpPaintPerfData {
  // LCP
  lcpTime: number
}

export interface ILcpPaintPerf {
  (callback: (info: ILcpPaintPerfData) => void): void
}

export type AnalyticsPerfData =
  | { type: 'navigation', data: INavigationPerfData }
  | { type: 'paint', data: IPaintPerfData }
  | { type: 'lcp-paint', data: ILcpPaintPerfData }

export interface IAnalyticsPerf {
  (callback?: (info: AnalyticsPerfData) => void): void
}
