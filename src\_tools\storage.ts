const session = window.sessionStorage
const local = window.localStorage

const setSessionStorage = (key: string, val: any) => {
  const value = val ? JSON.stringify(val) : ''
  session.setItem(key, value)
}

const getSessionStorage = (key: string) => {
  const val = session.getItem(key)
  try {
    return val ? JSON.parse(val) : null
  } catch (e) {
    return val
  }
}

const removeSessionStorage = (key: string) => {
  session.removeItem(key)
}

const setLocalStorage = (key: string, val: any) => {
  const value = val ? JSON.stringify(val) : ''
  local.setItem(key, value)
}

const getLocalStorage = (key: string) => {
  const val = local.getItem(key)
  try {
    return val ? JSON.parse(val) : null
  } catch (e) {
    return val
  }
}

const removeLocalStorage = (key: string) => {
  local.removeItem(key)
}

export const storage = (name: string, isLocalStorage?: boolean) => {
  if (isLocalStorage) {
    return {
      get () {
        return getLocalStorage(name)
      },
      set (value: any) {
        return setLocalStorage(name, value)
      },
      del () {
        return removeLocalStorage(name)
      }
    }
  } else {
    return {
      get () {
        return getSessionStorage(name)
      },
      set (value: any) {
        return setSessionStorage(name, value)
      },
      del () {
        return removeSessionStorage(name)
      }
    }
  }
}
