# URL 编码问题修复说明

## 问题描述

在 `higher.ts queryStatus` 执行后，URL 中的 `+` 符号被错误地转换为 `%20`（空格的 URL 编码）。

**原始 URL:**
```
http://localhost:9000/itf-fi-core-web/zbank/buy0YuanChangeNew?estimatePrice=+GZcPU308rwRyjx4DMGIOQ==
```

**问题 URL:**
```
http://localhost:9000/itf-fi-core-web/zbank/buy0YuanChangeNew?estimatePrice=%20GZcPU308rwRyjx4DMGIOQ%3D%3D
```

## 根本原因

`qs` 库的 `stringify` 函数默认使用 `encodeURIComponent` 进行 URL 编码，会将 `+` 符号编码为 `%2B`，将空格编码为 `%20`。但在 URL 查询参数中，`+` 符号通常用来表示空格，所以当 `qs` 解析 URL 时会将 `+` 解释为空格，然后在重新序列化时将空格编码为 `%20`。

## 解决方案

创建了一个自定义的 URL 编码器 `customUrlEncoder`，它：

1. 首先使用标准的 `encodeURIComponent` 进行编码
2. 然后将 `%20`（空格编码）替换回 `+`，保持 `+` 符号的原始含义

## 修改的文件

### 1. `src/url/index.ts`
- 添加了 `customUrlEncoder` 函数
- 更新了 `urlAppend` 函数使用自定义编码器
- 添加了调试日志

### 2. `src/login/higher.ts`
- 更新了 `removeSensitiveParams` 函数使用自定义编码器
- 添加了详细的调试日志来追踪 URL 变化

### 3. `src/login/api-h5.ts`
- 更新了 `wqbH5Status` 函数中的 `qsStringify` 调用使用自定义编码器
- 添加了调试日志

### 4. `src/index.ts`
- 导出了 `customUrlEncoder` 函数供外部使用

## 调试日志

添加了以下调试日志来帮助追踪问题：

1. `higher.ts` 中的各个关键点
2. `utils.ts` 中的参数处理
3. `url/index.ts` 中的 URL 解析和构建
4. `api-h5.ts` 中的查询字符串处理

## 测试

创建了 `test-url-encoding.html` 文件来验证修复效果。

## 使用方法

现在所有涉及 URL 查询参数处理的地方都会自动使用自定义编码器，保持 `+` 符号不被错误编码。

如果需要在其他地方使用相同的编码逻辑，可以导入 `customUrlEncoder`：

```typescript
import { customUrlEncoder } from 'commonkit'

// 或者
import { customUrlEncoder } from './url'
```
