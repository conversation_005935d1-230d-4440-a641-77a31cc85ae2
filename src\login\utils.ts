import { getUrlParams } from '../url'
import { loginParamsStorageForUser, loginParamsStorage } from './config'
import { ILoginJumpUrlHookData, IRemoveLoginParams, IObj } from '../types'

export const shared = {
  timestamp: new Date().getTime(),
  jumpHookCallback: (data: ILoginJumpUrlHookData, next: Function) => { next() },
  loginPageInstance: null
}

/**
 * 把多端组 bizFrom 数字码转为标准渠道 source 字符串码
 * @param bizFrom
 */
const bizFromToSource = (bizFrom: string) => {
  // source 必须传递英文单词格式，如果是新增得渠道，可以为数字格式
  // 针对一些可能的数字，进行转义
  const sourceMap: Record<string, string> = {
    226: 'h5_mz',
    225: 'app_sjyyt',
    2251: 'app_sjyyt',
    2252: 'app_sjyyt',
    212: 'app_wqb',
    219: 'h5_banner',
    224: 'wx_gzh_wqb',
    229: 'h5_ydsyt',
    240: 'web_df',
    241: 'wx_gzh_lt_neimenggu',
    242: 'wx_gzh_lt_beijing',
    243: 'wx_gzh_lt_tianjin',
    244: 'wx_gzh_lt_shandong',
    245: 'wx_gzh_lt_hebei',
    246: 'wx_gzh_lt_shanxi',
    247: 'wx_gzh_lt_anhui',
    248: 'wx_gzh_lt_shanghai',
    249: 'wx_gzh_lt_jiangsu',
    250: 'wx_gzh_lt_zhejiang',
    251: 'wx_gzh_lt_fujian',
    252: 'wx_gzh_lt_hainan',
    253: 'wx_gzh_lt_guangdong',
    254: 'wx_gzh_lt_guangxi',
    255: 'wx_gzh_lt_qinghai',
    256: 'wx_gzh_lt_hubei',
    257: 'wx_gzh_lt_hunan',
    258: 'wx_gzh_lt_jiangxi',
    259: 'wx_gzh_lt_henan',
    260: 'wx_gzh_lt_xizang',
    261: 'wx_gzh_lt_sichuan',
    262: 'wx_gzh_lt_chongqing',
    263: 'wx_gzh_lt_shaanxi',
    264: 'wx_gzh_lt_guizhou',
    265: 'wx_gzh_lt_yunnan',
    266: 'wx_gzh_lt_gansu',
    267: 'wx_gzh_lt_ningxia',
    268: 'wx_gzh_lt_xinjiang',
    269: 'wx_gzh_lt_jilin',
    270: 'wx_gzh_lt_liaoning',
    271: 'wx_gzh_lt_heilongjiang',
    272: 'wx_gzh_lt_sfgs',
    273: 'wx_gzh_lt_beijing2',
    274: 'wx_gzh_lt_zyfdwyh',
    275: 'wx_gzh_lt_henankf',
    276: 'wx_gzh_lt_yuncheng',
    277: 'wx_gzh_lt_changzhi',
    278: 'wx_gzh_lt_wxf',
    279: 'wx_gzh_bjltxy',
    280: 'wx_gzh_sxwqy',
    402: 'wx_xcx_wqb'
  }
  // 返回映射值，默认返回输入值
  return sourceMap[bizFrom] || bizFrom
}
/**
 * 把标准渠道 source 字符串码转为 多端组 bizFrom 数字码
 * @param source
 */
const sourceToBizFrom = (source: string) => {
  const reverseMap: Record<string, string> = {
    h5_mz: '226',
    app_sjyyt: '225', // 2251, 2252 也属于该类别，但反向映射无法唯一确定
    app_wqb: '212',
    h5_banner: '219',
    wx_gzh_wqb: '224',
    h5_ydsyt: '229',
    web_df: '240',
    wx_gzh_lt_neimenggu: '241',
    wx_gzh_lt_beijing: '242',
    wx_gzh_lt_tianjin: '243',
    wx_gzh_lt_shandong: '244',
    wx_gzh_lt_hebei: '245',
    wx_gzh_lt_shanxi: '246',
    wx_gzh_lt_anhui: '247',
    wx_gzh_lt_shanghai: '248',
    wx_gzh_lt_jiangsu: '249',
    wx_gzh_lt_zhejiang: '250',
    wx_gzh_lt_fujian: '251',
    wx_gzh_lt_hainan: '252',
    wx_gzh_lt_guangdong: '253',
    wx_gzh_lt_guangxi: '254',
    wx_gzh_lt_qinghai: '255',
    wx_gzh_lt_hubei: '256',
    wx_gzh_lt_hunan: '257',
    wx_gzh_lt_jiangxi: '258',
    wx_gzh_lt_henan: '259',
    wx_gzh_lt_xizang: '260',
    wx_gzh_lt_sichuan: '261',
    wx_gzh_lt_chongqing: '262',
    wx_gzh_lt_shaanxi: '263',
    wx_gzh_lt_guizhou: '264',
    wx_gzh_lt_yunnan: '265',
    wx_gzh_lt_gansu: '266',
    wx_gzh_lt_ningxia: '267',
    wx_gzh_lt_xinjiang: '268',
    wx_gzh_lt_jilin: '269',
    wx_gzh_lt_liaoning: '270',
    wx_gzh_lt_heilongjiang: '271',
    wx_gzh_lt_sfgs: '272',
    wx_gzh_lt_beijing2: '273',
    wx_gzh_lt_zyfdwyh: '274',
    wx_gzh_lt_henankf: '275',
    wx_gzh_lt_yuncheng: '276',
    wx_gzh_lt_changzhi: '277',
    wx_gzh_lt_wxf: '278',
    wx_gzh_bjltxy: '279',
    wx_gzh_sxwqy: '280',
    wx_xcx_wqb: '402'
  }
  // 返回映射值，默认返回输入值
  return reverseMap[source] || source
}

/**
 * 获取合并后的url中参数及缓存中的参数（内部使用）
 * 此接口不仅要提供登录数据，其他接口调用还需要把登录参数进行缓存
 * 本方法仅限内部使用，仅在当前浏览中缓存，刷新后部分缓存会消失
 * @param useCache
 */
export const getLoginParams = (useCache = false) => {
  // 获取url中的参数
  const urlParams = getUrlParams()
  // 获取缓存中的参数
  const session = loginParamsStorage.get() || {}
  const cache = useCache ? session : {}
  const originSource = urlParams.source || urlParams.biz_channel_code || session.source || ''
  // 所有字段均要缓存，因为后续会移除url中隐私参数，则 urlParams 拿不到了
  return {
    channelCode: cache.channelCode || urlParams.channelCode || '',
    source: bizFromToSource(originSource),
    originSource,
    bizFrom: sourceToBizFrom(originSource),
    ticket: urlParams.ticket || cache.ticket || urlParams.accessToken || '', // accessToken 联通行政渠道专属，按ticket透传
    unionSessionId: urlParams.unionSessionId || cache.unionSessionId || '',
    rptid: urlParams.rptid || urlParams.rpt || cache.rptid || '', // 沃钱包客户端，可以配置带 rptid 跳转业务，此时字段名字是 rpt，要兼容
    itfAuthCookieWX: cache.itfAuthCookieWX || urlParams.itfAuthCookieWX || '',
    type: urlParams.type || cache.type || '',
    time: new Date()
  }
}

/**
 * 获取合并后的url中参数及缓存中的参数（外部使用）
 */
export const getLoginParamsForUser = () => {
  // 获取url中的参数
  const urlParams = getUrlParams()
  // 获取缓存中的参数
  const session = loginParamsStorageForUser.get() || {}
  const originSource = urlParams.source || urlParams.biz_channel_code || session.source || ''
  return {
    channelCode: session.channelCode || urlParams.channelCode || '',
    source: bizFromToSource(originSource),
    originSource,
    bizFrom: sourceToBizFrom(originSource),
    ticket: urlParams.ticket || session.ticket || '',
    unionSessionId: urlParams.unionSessionId || session.unionSessionId || '',
    rptid: urlParams.rptid || urlParams.rpt || session.rptid || '', // 沃钱包客户端，可以配置带 rptid 跳转业务，此时字段名字是 rpt，要兼容
    itfAuthCookieWX: session.itfAuthCookieWX || urlParams.itfAuthCookieWX || '',
    type: urlParams.type || session.type || '',
    time: new Date()
  }
}

/**
 * 获取不敏感的 URL 参数（移除敏感参数）
 * @param params
 */
export const getInsensitiveParams = (params?: IObj) => {
  let urlParams
  if (params) {
    urlParams = params
    console.log('utils.ts getInsensitiveParams using provided params:', JSON.stringify(params))
  } else {
    // 获取url中的参数
    urlParams = getUrlParams()
    console.log('utils.ts getInsensitiveParams using getUrlParams():', JSON.stringify(urlParams))
  }
  const { ticket, unionSessionId, rptid, rpt, ...others } = urlParams as IRemoveLoginParams
  console.log('utils.ts getInsensitiveParams removed sensitive params, remaining:', JSON.stringify(others))
  return {
    has: !!(ticket || unionSessionId || rptid || rpt),
    params: others
  }
}
