declare global {
  interface Window {
    ms: any
    setClientInfo: any
    js_invoke: any
    bridge: any
  }
}

export interface IClinetInfoCookie {
  Domain: string
  Name: string
  Value: string
  Path: string
}

export interface IUnicomClientInfo {
  cookies: IClinetInfoCookie[]
  currentPhoneNumber: string
  imei: string
  clientVersion: string
  osVersion: string
  osVersionCode: string
  pip: string
  netWay: string
  deviceBrand: string
  deviceModel: string
  statusBar: string
  statusBarHeight: string
  yw_code: string
  platformToken: string
  provinceCode: string
  cityCode: string
  locateProvinceCode: string
  locateCityCode: string
  locateCityName: string
}

export interface IUnicomFaceConfig {
  type: string
  params: {
    pageTitle: string
    actionCount?: 1 | 2 | 3
    otherEnvImgCount?: 0 | 1 | 2
    customActions?: number[]
    showFailToast?: boolean
    needVideo?: boolean
    needNei?: boolean
  }
}

export interface IUnicomFaceResult {
  resultCode: string
  resultInfo: any
}
