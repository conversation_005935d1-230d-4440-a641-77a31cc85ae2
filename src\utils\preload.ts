/**
 * 预加载
 * @param url
 * @param timeout
 */
export const preload = (url: string, timeout = 1000) => new Promise((resolve) => {
  if (!url) {
    resolve(0)
  }
  let isResolve = false
  const img = new Image()
  img.onload = () => {
    if (!isResolve) {
      isResolve = true
      resolve(1)
    }
  }
  img.onerror = () => {
    if (!isResolve) {
      isResolve = true
      resolve(2)
    }
  }
  setTimeout(() => {
    if (!isResolve) {
      isResolve = true
      resolve(3)
    }
  }, timeout)
  img.src = url
})
