<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL 编码测试</title>
</head>
<body>
    <h1>URL 编码测试</h1>
    <div id="results"></div>

    <script>
        // 模拟 qs 库的 stringify 函数
        function qsStringify(obj, options = {}) {
            const encoder = options.encoder || encodeURIComponent;
            return Object.keys(obj)
                .map(key => `${encoder(key)}=${encoder(obj[key])}`)
                .join('&');
        }

        // 自定义编码器
        function customUrlEncoder(str) {
            const encoded = encodeURIComponent(str);
            return encoded.replace(/%20/g, '+');
        }

        // 测试数据
        const testParams = {
            estimatePrice: '+GZcPU308rwRyjx4DMGIOQ==',
            normalParam: 'test value',
            plusSign: 'value+with+plus'
        };

        const results = document.getElementById('results');
        
        // 测试默认编码
        const defaultEncoded = qsStringify(testParams);
        results.innerHTML += `<h3>默认编码:</h3><p>${defaultEncoded}</p>`;
        
        // 测试自定义编码
        const customEncoded = qsStringify(testParams, { encoder: customUrlEncoder });
        results.innerHTML += `<h3>自定义编码 (保持+符号):</h3><p>${customEncoded}</p>`;
        
        // 测试原始 URL
        const originalUrl = 'http://localhost:9000/itf-fi-core-web/zbank/buy0YuanChangeNew?estimatePrice=+GZcPU308rwRyjx4DMGIOQ==';
        results.innerHTML += `<h3>原始 URL:</h3><p>${originalUrl}</p>`;
        
        // 测试解析和重新构建
        const url = new URL(originalUrl);
        const params = {};
        url.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        
        const rebuiltQuery = qsStringify(params, { encoder: customUrlEncoder });
        const rebuiltUrl = `${url.origin}${url.pathname}?${rebuiltQuery}`;
        results.innerHTML += `<h3>重新构建的 URL:</h3><p>${rebuiltUrl}</p>`;
        
        console.log('测试完成');
        console.log('默认编码:', defaultEncoded);
        console.log('自定义编码:', customEncoded);
        console.log('重新构建的 URL:', rebuiltUrl);
    </script>
</body>
</html>
