<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL 编码问题调试</title>
</head>
<body>
    <h1>URL 编码问题调试</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');

        // 测试当前页面的 URL 解析
        results.innerHTML += `<h3>当前页面信息:</h3>`;
        results.innerHTML += `<p>window.location.href: ${window.location.href}</p>`;
        results.innerHTML += `<p>window.location.search: ${window.location.search}</p>`;

        // 测试 URL 对象解析
        if (window.location.search) {
            const url = new URL(window.location.href);
            results.innerHTML += `<h3>URL 对象解析:</h3>`;
            url.searchParams.forEach((value, key) => {
                results.innerHTML += `<p>${key}: ${value}</p>`;
            });
        }

        // 测试 qs 库解析（如果可用）
        if (typeof window.qs !== 'undefined') {
            const parsed = window.qs.parse(window.location.search.substring(1));
            results.innerHTML += `<h3>qs 库解析:</h3>`;
            results.innerHTML += `<p>${JSON.stringify(parsed)}</p>`;
        }

        // 测试原始字符串解析
        if (window.location.search) {
            results.innerHTML += `<h3>原始字符串分析:</h3>`;
            const search = window.location.search.substring(1);
            const pairs = search.split('&');
            pairs.forEach(pair => {
                const [key, value] = pair.split('=');
                results.innerHTML += `<p>原始: ${key}=${value}</p>`;
                results.innerHTML += `<p>解码: ${decodeURIComponent(key)}=${decodeURIComponent(value)}</p>`;
            });
        }

        console.log('调试信息已显示');
    </script>
</body>
</html>
