import { log } from '../_tools/log'
import { useLogin } from './higher'
import { getInsensitiveParams, shared } from './utils'
import { IVueHelperInterceptorNext, IVueRouterGuard } from '../types'

export const beforeEachLoginInterceptor = (opts: IVueRouterGuard) => {
  const {
    to,
    from,
    next,
    loginRouteName,
    autoQueryStatus,
    loginType,
    handler
  } = opts

  // 当前是否超时自动跳转到登录页（状态缓存，如果跳转到登录页，则需要等登录页完成后才可以走下一步跳转）
  let hasJumpLoginPage = false
  // 拦截器线程随机数（排查线程问题使用）
  const rnd = Math.random().toString(36).slice(2)
  console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-1 ${window.location.href}`)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor window.location.search:', window.location.search)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor to.query:', JSON.stringify(to.query), to.query)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor to.fullPath:', to.fullPath)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor to.path:', to.path)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor from.fullPath:', from.fullPath)
  console.log('VueHelperRouter.ts beforeEachLoginInterceptor from.path:', from.path)
  log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor', 'rnd=' + rnd, from.name, '->', to.name)
  // 拦截器回调到后续页面
  const nextFn: IVueHelperInterceptorNext = (status, params) => {
    console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-2 ${window.location.href}`)
    console.log('VueHelperRouter.ts nextFn params:', JSON.stringify(params))
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor', 'rnd=' + rnd, '拦截器完成，回调')
    if (handler) {
      handler({
        status,
        params,
        needLogin: !!(to.meta && to.meta.login),
        isLogin: status === 2
      })
    } else {
      console.log('VueHelperRouter.ts nextFn calling next() with params:', JSON.stringify(params))
      console.log(`VueHelperRouter.ts nextFn before next() URL: ${window.location.href}`)
      next(params)
      console.log(`VueHelperRouter.ts nextFn after next() URL: ${window.location.href}`)

      // 添加延迟检查，看看 URL 什么时候被修改
      setTimeout(() => {
        console.log(`VueHelperRouter.ts nextFn 10ms later URL: ${window.location.href}`)
      }, 10)

      setTimeout(() => {
        console.log(`VueHelperRouter.ts nextFn 50ms later URL: ${window.location.href}`)
      }, 50)

      setTimeout(() => {
        console.log(`VueHelperRouter.ts nextFn 100ms later URL: ${window.location.href}`)
      }, 100)
    }
  }

  // 跳转登录页及进行登录
  const nextToRunLogin = (status: number, extParams: any) => {
    console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-3 ${window.location.href}`)
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 跳转到登录页及进行登录')

    const gotoPage = () => {
      // from.name
      // 为 null 时，此时为第三方跳转到本项目中，路由应该用 replace
      // 有内容时，为本项目其他页面跳转过来，直接 push 即可
      const replace = !from.name
      const query = to.query
      console.log('VueHelperRouter.ts gotoPage to.query:', JSON.stringify(query))
      console.log('VueHelperRouter.ts gotoPage to.fullPath:', to.fullPath)
      console.log('VueHelperRouter.ts gotoPage window.location.href:', window.location.href)
      console.log('VueHelperRouter.ts gotoPage window.location.search:', window.location.search)
      console.log('VueHelperRouter.ts gotoPage before getInsensitiveParams')
      const newQuery = getInsensitiveParams(query).params
      console.log('VueHelperRouter.ts gotoPage newQuery after getInsensitiveParams:', JSON.stringify(newQuery))
      nextFn(status, {
        name: loginRouteName,
        query: {
          ...newQuery,
          // 有的项目使用的是 /name/:id 形式，不能用 to.name 进行跳转，会导致 :id 丢失，故改成 path 形式
          callbackUrl: encodeURIComponent(to.path)
        },
        params: { ...to.params, ...extParams },
        replace
      })
    }

    const loginPageInstance = shared.loginPageInstance as any
    try {
      if (loginPageInstance && loginPageInstance.$route.name === loginRouteName) {
        console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-4 ${window.location.href}`)
        log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 跳转到登录页及进行登录。执行逻辑')
        loginPageInstance.getInitData()
      } else {
        console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-5 ${window.location.href}`)
        log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 跳转到登录页及进行登录。跳转页面')
        gotoPage()
      }
    } catch (error) {
      gotoPage()
    }
  }

  // status 用户登录状态，0-未检查，-1-登录错误，1-未登录，2-已登录
  console.log(`VueHelperRouter.ts beforeEachLoginInterceptor before useLogin ${window.location.href}`)
  const [state, queryStatus] = useLogin()
  console.log(`VueHelperRouter.ts beforeEachLoginInterceptor after useLogin ${window.location.href}`)

  console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-6 ${window.location.href}`)
  // 当前页面就是登录页，不做处理
  if (to.name === loginRouteName) {
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 将跳转到登录页，不做处理')
    nextFn(state.status)
  } else if (from.name === loginRouteName) {
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 从登录页跳走，不做处理')
    nextFn(state.status)
  } else if (to.meta && to.meta.login) {
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 需要登录')
    // 非登录页，页面需要登录，则需要跳转登录页
    // 需要先刷新登录状态
    if (state.status <= 0 && autoQueryStatus) {
      // 1000ms 后跳转登录（并带有params，让登录页不要操作流程），防止用户卡在登录查询等步骤上
      setTimeout(() => {
        if (state.status !== 2) {
          log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 自动跳转登录页')
          hasJumpLoginPage = true
          nextToRunLogin(0, { noLogic: true })
        }
      }, 1000)
      console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-9 before queryStatus ${window.location.href}`)
      queryStatus({ loginType }).then(() => {
        console.log(`VueHelperRouter.ts beforeEachLoginInterceptor-9 after queryStatus ${window.location.href}`)
        if (state.status === 2) {
          log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 需要登录，登录成功', '当前是否进入登录页：' + hasJumpLoginPage)
          if (hasJumpLoginPage) {
            // 已经跳转到登录页，则必须使用登录页的下一步逻辑
            const triggerLoginPageNext = () => {
              const loginPageInstance = shared.loginPageInstance as any
              if (loginPageInstance) {
                if (loginPageInstance.$route.name === loginRouteName) {
                  log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 需要登录，登录成功，执行跳转后续页面逻辑')
                  loginPageInstance.nextPage()
                }
                return true
              } else {
                return false
              }
            }
            const interval = setInterval(() => {
              if (triggerLoginPageNext()) clearInterval(interval)
            }, 100)
          } else {
            nextFn(state.status)
          }
        } else {
          // 未登录，跳转登录，由于此时可能已经在登录页了，故需要调用登录页面逻辑
          log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 需要登录，登录失败')
          nextToRunLogin(0, {})
        }
      })
    } else {
      log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 需要登录，当前未查询登录状态或已登录')
      nextFn(state.status)
    }
  } else {
    // 非登录页，无需登录，直接放行
    log('VUE_HELPER_LOGIN-beforeEachLoginInterceptor 非登录页，无需登录')
    nextFn(state.status)
  }
}
