{"name": "commonkit", "version": "9.14.0", "description": "前端开发工具包", "scripts": {"build": "npm run lint && npm run clear && vite build && npm run copy", "clear": "rimraf ./lib", "copy": "node ./copy-files.js", "lint": "eslint src --ext .ts --fix"}, "repository": {"type": "git", "url": "https://gitcode.unicompayment.com/itf/itf-fe/commonkit"}, "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/types/index.d.ts", "sideEffects": false, "keywords": [], "author": "yukapril", "license": "ISC", "dependencies": {"axios": "^1.8.4", "ito-ffms-client-api": "1.0.8", "lodash": "^4.17.21", "qs": "^6.13.1", "url-parse": "^1.5.10"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@types/core-js": "^2.5.8", "@types/lodash": "^4.17.16", "@types/qs": "^6.9.18", "@types/url-parse": "^1.4.11", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-vue2": "^2.3.3", "eslint": "^8.57.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.6.0", "fs-extra": "^11.3.0", "postcss": "8.4.49", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "typescript": "5.6.3", "vite": "^5.4.9", "vite-plugin-dts": "^4.4.0", "vue": "2.7.16", "vue-router": "^3.6.5", "vue-template-compiler": "2.7.16"}, "files": ["lib", "src", "src/types", "package.json", "tsconfig.json"], "publishConfig": {"registry": "http://nexus.unicompayment.com/repository/npm-hosted/"}, "engines": {"node": ">=16.0.0"}}