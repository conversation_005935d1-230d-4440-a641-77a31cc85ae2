import { getLoginParamsForUser, shared } from './utils'
import LoginComponent from './LoginComponent.vue'
import { loginCifId, loginZqInfo } from './config'
import { ILoginJumpUrlHook } from '../types'

export { LoginComponent }
export { status, login } from './base'
export { useLogin, loginRun, loginCallback } from './higher'
export { beforeEachLoginInterceptor } from './VueHelperRouter'
export { loginTimer } from './config'
export { WopayLoginType, RptidUpdateType, LoginType } from './enum'

/**
 * 获取 url 中的登录参数
 */
export const getUrlLoginParams = getLoginParamsForUser

/**
 * 获取登录后的用户号
 */
export const getCifId = () => loginCifId.get()

/**
 * 获取登录后的政企信息（需要登录时配置查询才可获取到）
 */
export const getZqInfo = () => loginZqInfo.get()

/**
 * 登录跳转 woauth 的 url 拦截
 * @param callback
 */
export const loginJumpHook: ILoginJumpUrlHook = (callback) => {
  if (callback) shared.jumpHookCallback = callback
}
