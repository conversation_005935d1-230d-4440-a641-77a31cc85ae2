import { WopayClientApi } from '../WopayClientApi'
import { IShareOptions } from '../types'

export default (options: IShareOptions, userData?: any) => {
  const { link, title, describe, picUrl, channel, callback } = options
  const channelArr: string[] = []

  if (channel && channel.length > 0) {
    // 手动选择渠道
    channel.forEach(ch => {
      channelArr.push(ch)
    })
  } else {
    // 全部渠道
    // 沃钱包如果不传渠道内容，默认渠道可能缺少“短信”
    channelArr.push('WXFriend')
    channelArr.push('FriendsCircle')
    channelArr.push('QQFriend')
    channelArr.push('QQZone')
    channelArr.push('SMS')
    channelArr.push('copy')
  }

  WopayClientApi.share({
    type: 'url',
    title,
    picUrl,
    describle: describe,
    link: link || window.location.href,
    channel: channelArr,
    success: () => {
      // eslint-disable-next-line n/no-callback-literal
      callback && callback({ status: true, type: 'wopay', errCode: 0 }, userData)
    },
    failed: () => {
      // eslint-disable-next-line n/no-callback-literal
      callback && callback({ status: false, type: 'wopay', errCode: 10 }, userData)
    }
  })
}
