<template>
  <div class="login-page" v-if="show" style="padding:80px 17px 0;text-align:center;">
    <img :src="errorImg" alt="处理中" @click="onOneClick" style="width:210px;">
    <p v-if="!err" @click="onTwoClick" style="font-size:14px;color:#B1BEC9;">加载中...</p>
    <p v-if="err" @click="onTwoClick" style="font-size:14px;color:#B1BEC9;">加载失败，请刷新重试</p>
    <button
        @click="onClick"
        style="margin-top:20px;padding:9px 0;width:100px;border:1px solid #9998;border-radius:23px;font-size:14px;color:#000;">
      刷新
    </button>
    <div v-if="debugMode" style="margin-top:10px;text-align:left;">
      <p style="padding-bottom: 4px;font-size: 12px;" v-for="(s,index) in stack" :key="index">{{ s }}</p>
    </div>
  </div>
</template>

<script>
import { isWopay } from '../env'
import { useLogin } from './higher'
import { getInsensitiveParams, shared } from './utils'
import { logger, log } from '../_tools/log'
import errorImg from './error'

export default {
  props: {
    isLocal: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => ({})
    },
    waitingTime: {
      type: Number,
      default: 1000
    }
  },
  data () {
    return {
      errorImg, // 错误图片，使用 base64 形式，防止路径问题导入失败
      show: false, // 是否展示页面内容
      err: false, // 是否失败状态
      stack: [], // 错误详情
      debug: {
        click1: 0,
        click2: 0
      } // 调试器对象
    }
  },
  computed: {
    debugMode () {
      return this.debug.click1 === 5 && this.debug.click2 >= 5
    }
  },
  methods: {
    onOneClick () {
      this.debug.click1++
    },
    onTwoClick () {
      this.debug.click2++
    },
    nextPage () {
      const params = this.$route.params
      const {
        callbackUrl,
        ...otherQuery
      } = this.$route.query
      const newQuery = getInsensitiveParams(otherQuery).params
      this.$router.replace({
        // 有的项目使用的是 /name/:id 形式，不能用 name 进行跳转，会导致 :id 丢失，故改成 path 形式
        path: decodeURIComponent(callbackUrl || ''),
        query: newQuery,
        params
      })
    },
    login () {
      const [state, queryStatus, toLogin] = useLogin()
      // 检查登录
      log('登录页，执行登录状态检查')
      console.log(`LoginComponent.vue login ${window.location.href}`)
      queryStatus(this.options).then(() => {
        if (state.status === 2) {
          // 直接回调后续页面
          this.$emit('state', state)
          log('登录页，登录完成，跳转后续页面')
          this.nextPage()
        } else {
          // 去登录
          const loginOpts = {
            isLocal: this.isLocal,
            loginType: 'before', // 登录前置标志
            callback: (loginStatus) => {
              if (loginStatus === 2) {
                this.$emit('state', state)
                this.nextPage()
              } else {
                // 登录系统异常情况
                this.err = true
                this.$emit('state', state)
              }
            }
          }
          log('登录页，拉起登录')
          toLogin(loginOpts, this.options)
        }
      })
    },
    onClick () {
      this.err = false
      this.login()
    },
    // 获取数据
    getInitData () {
      this.login()
    }
  },
  created () {
    // 沃钱包APP 需要设置特殊标题
    if (isWopay) document.title = '\u200E'
    // 设置登录页实例，后续拦截器会用到
    shared.loginPageInstance = this

    logger.bind(data => {
      this.stack = data
    })

    // 两秒后显示登录按钮，防止点击客户端关闭登录框
    setTimeout(() => {
      this.show = true
    }, this.waitingTime)

    // 如果是拦截器自动跳转过来，会带有 noLogic 参数，此时不需要执行逻辑（拦截器页自动执行后续逻辑）
    if (this.$route.params.noLogic) {
      log('登录页，拦截器自动跳转，不执行登录逻辑')
      return
    }

    this.getInitData()
  }
}
</script>
