import { ENV_APP_NAME } from '../_tools/envAppName'
import { woReport } from '../_tools/reporter'
import { PrintTypes } from '../types'

function print (printType: PrintTypes, ...args: any[]) {
  args.unshift(`[${ENV_APP_NAME()}]`)
  console[printType].apply(console, args)
}

export function log (...args: any[]) {
  print('log', ...args)
}

export function debug (...args: any[]) {
  print('debug', ...args)
}

export function warn (...args: any[]) {
  print('warn', ...args)
}

export function error (...args: any[]) {
  print('error', ...args)
  woReport(...args)
}
