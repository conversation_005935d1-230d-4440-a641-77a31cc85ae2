import { loadMs, ms } from './ms'
import { log } from '../_tools/log'
import { IUnicomFaceConfig, IUnicomFaceResult } from '../types'

export const msFaceHelper = (config: IUnicomFaceConfig) => new Promise<IUnicomFaceResult>(resolve => {
  loadMs().then(() => {
    ms.exec('FacePlusPlus', config).then((result: any) => {
      /**
       * iOS 不论成功与失败，均进入这个回调
       * 活体成功返回数据结构如下：
       * result: {
       *   resultCode: "0", // 0表示活体成功，1失败
       *   // 动作照片张数由动作个数和额外全景照片数决定，例如一个活体动作则总张数为：3张基本照片 + 额外全景照片
       *   // resultInfo 安卓是字符串格式，需要 parse，iOS 直接为对象形式
       *   resultInfo: {
       *     image_best: "", // 裁剪过的正脸图
       *     image_env: "", // 未裁剪的正脸图,（9.1.1添加图片溯源code，code从网络获取，获取失败时则不加code）
       *     image_action_1: "", // 动作图
       *     image_action_2: "",
       *     image_action_3: "",
       *     image_env0: "",
       *     image_env1: "",
       *     videoPath: "", // needVideo为true，返回视频本地地址，否则不返回该字段（8.9.1新增功能）
       *     imgCode: "", //（9.1.1 image_env添加的图片溯源code，code从网络获取，获取失败时code为""）
       *     faceProcId: "", //（9.1.1图片溯源流水号，id从网络获取，获取失败时id为""）
       *     hasImgCode: "y", //（9.1.1 image_env图片添加溯源code标记，y为已添加，n未添加）
       *     nei: "" //（10.3版本【0-100 模拟器-真机】只有安卓有这个返回值）
       *   }
       * }
       */

      log('手厅 Face Success:', result)
      let info = result.resultInfo
      if (result.resultInfo && typeof result.resultInfo === 'string') {
        try {
          info = JSON.parse(result.resultInfo)
        } catch (e) {}
      }
      resolve({ resultCode: result.resultCode, resultInfo: info })
    }).catch((e: any) => {
      /**
       * 安卓失败会进入此回调
       * 活体失败数据结构如下：
       * result: {
       *   resultCode: "1", // 0表示活体成功，1失败
       *   // resultInfo 安卓是字符串格式，需要 parse，iOS 直接为对象形式
       *   resultInfo: {
       *     error: "超时失败" // 活体失败原因、用户取消
       *   }
       * }
       */
      log('手厅 Face Fail:', e)
      const result = e.data
      let info = result.resultInfo
      if (result.resultInfo && typeof result.resultInfo === 'string') {
        try {
          info = JSON.parse(result.resultInfo)
        } catch (e) {}
      }
      resolve({ resultCode: result.resultCode, resultInfo: info })
    })
  })
})
