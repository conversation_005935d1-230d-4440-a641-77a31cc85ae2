// 统一采用公共 js 逻辑，本模块实现拦截等处理，将风控数据存储一份于 cookie 中
// 风控提供的完整二合一（同盾、芯盾）加载包：
// CDN 源代码（生产）：https://media.cdn.unicompayment.com/ts-sss/oss/public/fingerprint/fp-pro/fobid-all.js
// CDN 源代码（测试）：https://res.cdn.unicompayment.com/media/ts-sss/oss/public/fingerprint/fp-test/fobid-all.js
//
// 风控提供的独立（芯盾）加载包（本项目内不使用这个）：
// 芯盾（生产）https://media.cdn.unicompayment.com/ts-sss/oss/public/fingerprint/fp-pro/fobid-unicom.js
// 芯盾（测试）https://res.cdn.unicompayment.com/media/ts-sss/oss/public/fingerprint/fp-test/fobid-unicom.js
import { loadJs } from '../_tools/loader'
import { IRiskParams, RiskStatus, IRiskCallbackParams, IRiskCallbackObj } from '../types'

/**
 * 监听对象属性变化
 * @param obj
 * @param key
 * @param observe
 */
const defineProperty = (obj: any, key: string, observe: Function) => {
  let data: any
  Object.defineProperty(obj, key, {
    set (val) {
      data = val
      observe(key, val, obj)
    },
    get () {
      return data
    },
    enumerable: true
  })
}

/**
 * 创建一个递归函数来为对象的每个属性设置监听
 * @param callback
 * @param timeout
 */
const proxy = (callback: IRiskCallbackParams, timeout: number) => {
  // 重建一个标准 Object，防止只有部分数据存在导致字段缺失
  const data: IRiskParams = {
    tdTokenId: '',
    unicomTokenId: '',
    tdBlackbox: ''
  }
  let status: RiskStatus = -1 // -1-初始值，0-正常返回，1-超时返回，2-接口异常

  let isCallback = false

  setTimeout(() => {
    if (isCallback) return
    isCallback = true
    status = 1
    callback && callback(data, status)
  }, timeout)

  const fn = (key: string, val: string, obj: IRiskParams) => {
    data.tdTokenId = obj.tdTokenId || ''
    data.unicomTokenId = obj.unicomTokenId || ''
    data.tdBlackbox = obj.tdBlackbox || ''

    if (data.unicomTokenId && data.tdTokenId) {
      // 启用异步操作，obj.tdBlackbox 在 obj.tdTokenId 之后才会生成
      // 尽量确保所有数据都已经生成
      setTimeout(() => {
        if (isCallback) return
        isCallback = true
        status = 0
        callback && callback(data, status)
      }, 0)
    }
  }

  try {
    // 劫持 window.unicomFobid
    defineProperty(window, 'unicomFobid', (val: any) => {
      // 劫持 window.unicomFobid 中的 unicomTokenId/tdTokenId
      defineProperty(window[val], 'unicomTokenId', fn)
      defineProperty(window[val], 'tdTokenId', fn)
    })
  } catch (e) {
    // 考虑 defineProperty 出错情况，可能内存中已经存在数据
    if (window.unicomFobid) {
      data.unicomTokenId = window.unicomFobid.unicomTokenId
      data.tdTokenId = window.unicomFobid.tdTokenId
      data.tdBlackbox = window.unicomFobid.tdBlackbox
      isCallback = true
      status = 2
      callback && callback(data, status)
    }
  }
}

export const getRiskId = (url: string, timeout: number = 5000) => new Promise<IRiskCallbackObj>(resolve => {
  proxy((data, status) => {
    resolve({ data, status })
  }, timeout)

  loadJs(
    url,
    // 不用关心加载成功，后续会拦截数据变化并进行回调
    () => {},
    (e: any) => {
      resolve({
        data: { tdTokenId: '', unicomTokenId: '', tdBlackbox: '' },
        status: 2,
        err: e
      })
    })
})
