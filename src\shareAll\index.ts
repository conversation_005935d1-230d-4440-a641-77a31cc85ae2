import { isWopay, isUnicom, isWeixin } from '../env'
import shareWopay from './shareWopay'
import shareUnicom from './shareUnicom'
import shareH5 from './shareH5'
import { shareWeixin, setData } from './shareWeixin'
import { landingUrl } from './config'
import { IShareOptions } from '../types'

/**
 * 分享初始化，目前主要是保存入口落地页 URL，微信使用
 */
export const shareInit = () => {
  // 签名URL，微信分享，iOS 需要对入口 URL 进行签名
  // 其他环境下，签名当前页URL
  const url = window.location.href.split('#')[0]
  landingUrl.set(url)
}

/**
 * （微信专用） 配置当前页面分享内容
 */
export const setWeiXinShareData = (options: IShareOptions, userData?: any) => {
  if (isWeixin) {
    return setData(options, userData)
  }
}

/**
 * 调用分享能力
 */
export const share = (options: IShareOptions, userData?: any) => {
  if (isWopay) {
    return shareWopay(options, userData)
  } else if (isUnicom) {
    return shareUnicom(options, userData)
  } else if (isWeixin) {
    return shareWeixin(options, userData)
  } else {
    return shareH5(options, userData)
  }
}
