import { IZhugeOptions, IObj } from '../types'

const zhugeLoad = () => {
  if (window.zhuge) return

  const addListener = Element.prototype.addEventListener
  window.zgclickhook = true

  function zgListener () {
    if (window.zhuge && window.zhuge.trackClick) window.zhuge.trackClick(arguments[0])
  }

  Element.prototype.addEventListener = function () {
    const etype = arguments[0]
    const listener = arguments[1]
    if (etype === 'click' && listener) {
      this.setAttribute('zgclickable', 'true')
      // @ts-ignore
      this.setAttribute('zghook', parseInt(this.getAttribute('zghook') || 0) + 1)
      // @ts-ignore
      if (parseInt(this.getAttribute('zghook')) === 1) addListener.call(this, 'click', zgListener)
    }
    // @ts-ignore
    return addListener.apply(this, arguments)
  }
  const removeListener = Element.prototype.removeEventListener
  Element.prototype.removeEventListener = function () {
    const etype = arguments[0]
    // @ts-ignore
    this.setAttribute('zghook', parseInt(this.getAttribute('zghook') || 0) - 1)
    // @ts-ignore
    if (etype === 'click' && parseInt(this.getAttribute('zghook')) <= 0) {
      this.removeAttribute('zgclickable')
      removeListener.call(this, etype, zgListener)
    }
    // @ts-ignore
    removeListener.apply(this, arguments)
  }
  window.zhuge = window.zhuge || []
  window.zhuge.methods = '_init identify track trackRevenue getDid getSid getKey setSuperProperty setUserProperties setWxProperties setPlatform'.split(' ')
  window.zhuge.factory = function (b: any) {
    return function () {
      const a = Array.prototype.slice.call(arguments)
      a.unshift(b)
      window.zhuge.push(a)
      return window.zhuge
    }
  }
  for (let i = 0; i < window.zhuge.methods.length; i++) {
    const key = window.zhuge.methods[i]
    window.zhuge[key] = window.zhuge.factory(key)
  }
  window.zhuge.load = function (b: string, x: any) {
    if (!document.getElementById('zhuge-js')) {
      const a = document.createElement('script')
      const verDate = new Date()
      const verStr = verDate.getFullYear().toString() + verDate.getMonth().toString() + verDate.getDate().toString()

      a.type = 'text/javascript'
      a.id = 'zhuge-js'
      a.async = !0
      // a.src = 'https://media.cdn.unicompayment.com/ito-ffms/res/zhuge.js?v=' + verStr // 多端组维护资源，可用
      a.src = 'https://media.cdn.unicompayment.com/dtc-uba/res/zhuge.js?v=' + verStr // 数科组维护资源，可用

      a.onerror = function () {
        window.zhuge.identify = window.zhuge.track = function (ename: any, props: any, callback: any) {
          if (callback && Object.prototype.toString.call(callback) === '[object Function]') {
            callback()
          } else if (Object.prototype.toString.call(props) === '[object Function]') {
            props()
          }
        }
      }
      const c: HTMLScriptElement = document.getElementsByTagName('script')[0]
      // @ts-ignore
      c.parentNode.insertBefore(a, c)
      window.zhuge._init(b, x)
    }
  }
}

const init = (key: string, options: IZhugeOptions) => {
  const defaultOpts: IZhugeOptions = {
    superProperty: {}, // 全局的事件属性(选填)
    adTrack: false, // 广告监测开关，默认为false
    zgsee: false, // 视屏采集开关， 默认为false
    autoTrack: false, // 启用全埋点采集（选填，默认false）
    singlePage: false, // 是否是单页面应用（SPA），启用autoTrack后生效（选填，默认false）
    debug: false
  }
  const opts = Object.assign(defaultOpts, options)
  window.zhuge.load(key, opts)
}

let defaultData = {}

/**
 * 设置默认数据
 * @param data
 */
export const zhugeAppendDefaultData = (data: IObj) => {
  defaultData = { ...defaultData, ...data }
}

/**
 * 诸葛初始化，并设置全局默认数据
 * @param key
 * @param options
 */
export const zhugeInit = (key: string, options: IZhugeOptions) => {
  zhugeLoad()
  init(key, options)
  window.zhuge.setSuperProperty(defaultData)
  return window.zhuge
}

/**
 * 诸葛埋点统计
 * @param name
 * @param data
 * @param callback
 */
export const zhugeTrack = (name: string, data?: any, callback?: Function) => new Promise(resolve => {
  const newData = { ...defaultData, ...data }
  window.zhuge.track(name, newData, () => {
    resolve(null)
    callback && callback()
  })
})
