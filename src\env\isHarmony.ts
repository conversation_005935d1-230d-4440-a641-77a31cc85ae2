import { ua } from './utils'

// 自带浏览器
// Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Saari/537.36 ArkWeb/4.1.6.1 Mobile HuaweiBrowser/1.2.10.300
// 手厅
// Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Saari/537.36 ArkWeb/4.1.6.1 Mobile; unicom{version:android@11.0600,desmobile:0};harmony{harmonyVersion:harmony@11.0600}

const check = (ua: string) => /ArkWeb|OpenHarmony|HarmonyOS/i.test(ua)
const ver = (ua: string) => ua.match(/OpenHarmony \/([\d+.]+)/i) || ''

export const isHarmonyOS = check(ua)
export const harmonyOSVer = ver(ua) ? ver(ua)[1] : ''
