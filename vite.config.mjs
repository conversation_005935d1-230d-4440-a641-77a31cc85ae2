import { resolve } from 'path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue2'
import dts from 'vite-plugin-dts'
import { babel } from '@rollup/plugin-babel'
// import { visualizer } from 'rollup-plugin-visualizer' // 导入可视化插件

export default defineConfig({
  build: {
    lib: {
      entry: [resolve(__dirname, 'src/index.ts')],
      name: 'CommonKit',
      formats: ['cjs', 'es'],
      // fileName: (format) => `bundle.${format}.js`,
      target: 'es2015'
    },
    rollupOptions: {
      external: ['qs', 'url-parse', 'axios', 'lodash', 'ito-ffms-client-api'],
      output: {
        dir: 'lib'
      }
    },
    // 必须关闭压缩，否则会把部分代码压缩为 catch{} 语法形式，同时出现 ?? 语法
    minify: false
  },
  plugins: [
    vue(),
    babel({
      babelHelpers: 'bundled',
      include: [/src/],
      exclude: [/node_modules/],
      presets: [
        [
          '@babel/preset-env',
          {
            targets: '> 0.1%, iOS >= 10, Android >= 5.0',
            modules: false,
            useBuiltIns: 'entry',
            corejs: 3
          }
        ]
      ],
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.vue']
    }),
    dts({ outDir: './lib/types' }),
    // visualizer({ // 添加可视化分析
    //   filename: 'stats.html', // 生成的 HTML 文件名
    //   open: true, // 构建完成后自动打开浏览器查看分析结果
    //   gzipSize: true, // 显示 gzip 压缩后的大小
    //   brotliSize: true // 显示 brotli 压缩后的大小
    // })
  ]
})
