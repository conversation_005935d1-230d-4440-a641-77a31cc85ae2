// 本地js文件为修改版，修改内容参考 https://www.cnblogs.com/mihoutaoguniang/p/15742455.html
// 源文件见此：https://res.wx.qq.com/open/js/jweixin-1.6.0.js

import { formPost } from '../_tools/request'
import { woReport } from '../_tools/reporter'
import { log, error } from '../_tools/log'
import { loadJs } from '../_tools/loader'
import { landingUrl, WX_APPID } from './config'
import { IShareApiData, IShareOptions } from '../types'

let wx: any = null
const loadWx = (next: Function) => {
  if (wx) {
    next(wx)
  } else {
    loadJs('https://res.wx.qq.com/open/js/jweixin-1.6.0.js', () => {
      wx = window.wx
      next(wx)
    }, () => {
      error('微信加载 sdk 失败')
    })
  }
}

/**
 * 通过接口获取微信参数
 */
const getParams = () => {
  const url = landingUrl.get() || window.location.href.split('#')[0]
  const postData = { currentUrl: encodeURIComponent(url) }
  return new Promise<[Error | null, IShareApiData]>(resolve => {
    formPost('/ci-mcss-party-front/v1/template/getShareInfoWeChat', postData).then(json => {
      // 返回结构：
      // {
      //     "code": "0000",
      //     "msg": "成功",
      //     "txId": "828480235312775168",
      //     "data": {
      //         "returnCode": "0",
      //         "returnMsg": "",
      //         "signature": "cfc6fe1c895f9dea9fd910e7d9176af1d544cafe",
      //         "jsapi_ticket": "HoagFKDcsGMVCIY2vOjf9i-LLiBToA4ucUah177nVwcjqdCpQOUx8-QpTxJFl1L0cJA7-zpl3dg--MtpUL_1gA",
      //         "nonceStr": "4bc32fd7-bf48-4b15-90a9-ef7b765025f9",
      //         "url": "http://localhost:9001/itf/fixcs/home",
      //         "timestamp": "1686636691"
      //     }
      // }
      resolve([null, json.data])
    }).catch(e => {
      resolve([e, { signature: '', jsapi_ticket: '', nonceStr: '', timestamp: '' }])
    })
  })
}

/**
 * 点击分享后触发
 * @param options
 * @param userData
 */
export const shareWeixin = (options: IShareOptions, userData?: any) => {
  loadWx(() => {
    const { callback, next, ...newOptions } = options
    next && next({ ...newOptions, type: 'weixin' }, userData)
  })
}

/**
 * 分享数据注入
 * @param options
 * @param userData
 */
export const setData = (options: IShareOptions, userData?: any) => {
  loadWx(async () => {
    const { link, title, describe, picUrl, callback } = options
    const [err, json] = await getParams()
    if (err) {
      // eslint-disable-next-line n/no-callback-literal
      callback && callback({ status: false, type: 'weixin', errCode: 30 }, userData)
      return
    }
    log('微信分享获取参数', json)
    // json 结构：
    // {
    //   "returnCode": "0",
    //   "returnMsg": "",
    //   "signature": "cfc6fe1c895f9dea9fd910e7d9176af1d544cafe",
    //   "jsapi_ticket": "HoagFKDcsGMVCIY2vOjf9i-LLiBToA4ucUah177nVwcjqdCpQOUx8-QpTxJFl1L0cJA7-zpl3dg--MtpUL_1gA",
    //   "nonceStr": "4bc32fd7-bf48-4b15-90a9-ef7b765025f9",
    //   "url": "http://localhost:9001/itf/fixcs/home",
    //   "timestamp": "1686636691"
    // }

    wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: WX_APPID, // 必填，公众号的唯一标识
      timestamp: json.timestamp, // 必填，生成签名的时间戳
      nonceStr: json.nonceStr, // 必填，生成签名的随机串
      signature: json.signature, // 必填，签名
      jsApiList: ['checkJsApi', 'updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表
    })

    // 微信调用后结果临时缓存
    const result: boolean[] = []

    // 微信调用后完成回调
    const resultCallback = () => {
      if (result.length === 2) {
        if (result[0] && result[1]) {
          log('微信分享 注入成功')
          // eslint-disable-next-line n/no-callback-literal
          callback && callback({ status: true, type: 'weixin', errCode: 0 }, userData)
        } else {
          let errCode = 0
          if (!result[0] && !result[1]) errCode = 33
          if (!result[0]) errCode = 31
          if (!result[1]) errCode = 32
          // eslint-disable-next-line n/no-callback-literal
          callback && callback({ status: false, type: 'weixin', errCode }, userData)
        }
      }
    }

    wx.ready(() => {
      wx.checkJsApi({
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'], // 需要检测的JS接口列表，所有JS接口列表见附录2
        success (res: any) {
          log('微信分享 wx.ready success', res)
          // 以键值对的形式返回，可用的api值true，不可用为false
          // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
          if (res.checkResult.updateAppMessageShareData) {
            wx.updateAppMessageShareData({
              title, // 分享标题
              desc: describe, // 分享描述
              link: link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: picUrl, // 分享图标
              success () {
                // 设置成功
                result[0] = true
                resultCallback()
              },
              fail (res: any) {
                log('微信分享 wx.updateAppMessageShareData fail', res)
                result[0] = false
                resultCallback()
              }
            })
          }
          if (res.checkResult.updateTimelineShareData) {
            wx.updateTimelineShareData({
              title, // 分享标题
              link: link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: picUrl, // 分享图标
              success () {
                // 设置成功
                result[1] = true
                resultCallback()
              },
              fail (res: any) {
                log('微信分享 wx.updateTimelineShareData fail', res)
                result[0] = false
                resultCallback()
              }
            })
          }
        },
        fail (res: any) {
          log('微信分享 wx.ready fail', res)
          // eslint-disable-next-line n/no-callback-literal
          callback && callback({ status: false, type: 'weixin', errCode: 34 }, userData)
        }
      })
    })

    wx.error((res: any) => {
      // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
      error('微信分享 wx.error', res)
      woReport('微信分享 wx.error', res)
      // eslint-disable-next-line n/no-callback-literal
      callback && callback({ status: false, type: 'weixin', errCode: 35 }, userData)
    })
  })
}
