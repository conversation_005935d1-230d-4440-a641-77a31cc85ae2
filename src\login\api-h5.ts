import { parse as qsParse, stringify as qsStringify } from 'qs'
import UrlParse from 'url-parse'
import { customUrlEncoder } from '../url'
import { isUnicom, isWopay } from '../env'
import { formPostLogin } from '../_tools/request'
import { woReport } from '../_tools/reporter'
import { log, warn } from '../_tools/log'
import { getInsensitiveParams, getLoginParams, shared } from './utils'
import { loginCifId, loginCifIdCache, loginH5JumpData, loginZqInfo, loginTimer, WQB_H5_API_URL } from './config'
import { RptidUpdateType } from './enum'
import {
  ILoginH5Resp,
  ILoginH5RespWrapped,
  ILoginInnerResp,
  ILoginJumpUrlHookJumpTimeFn,
  ILoginOptions,
  ILoginParams
} from '../types'

/**
 * 记录跳转时间，跳走前记录
 * @param source
 * @param type
 */
const recordJumpTime: ILoginJumpUrlHookJumpTimeFn = (source, type) => {
  const time = new Date().getTime()
  const data = { viaLogin: false, type, time, diff: 0, source }
  loginH5JumpData.set(data)
  return data
}

/**
 * 记录跳转时间，跳走返回后获取
 * @param source
 * @param type
 */
const getJumpTime: ILoginJumpUrlHookJumpTimeFn = (source, type) => {
  const cache = loginH5JumpData.get()
  if (cache) {
    // 存在缓存数据，则刚进行登录 login 完成后后返回
    loginH5JumpData.del()
    const time = new Date().getTime()
    const diff = Math.floor((time - cache.time) / 1000)
    return { viaLogin: true, type, time, diff, source }
  } else {
    // 不存在缓存数据，为默认登录成功的（没走过 login 过程）
    const time = new Date().getTime()
    return { viaLogin: false, type, time, diff: 0, source }
  }
}

/**
 * 沃钱包H5(M站)登录验证
 * @param opts opts.callbackUrl 回调URL(纯粹验证可不传，后续需要登录时必传)
 */
export const wqbH5Status = (opts: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  const { projectPath, callbackUrl, customParams = {}, rptidUpdate } = opts
  const loginParams = Object.assign({}, getLoginParams(true), customParams)

  const newCallback = callbackUrl || window.location.href
  console.log(`api-h5.ts wqbH5Status1-1 ${callbackUrl}`)
  console.log(`api-h5.ts wqbH5Status1-2 ${window.location.href}`)
  const callbackUrlParse = UrlParse(newCallback)
  // 如果原链接中带有rptid，则应该移除
  if (callbackUrlParse.query.indexOf('rptid') >= 0) {
    console.log(`api-h5.ts wqbH5Status2 ${callbackUrlParse.query.substring(1)}`)
    const query = qsParse(callbackUrlParse.query.substring(1), { decoder: str => decodeURIComponent(str) })
    const newQuery = getInsensitiveParams(query).params
    console.log('api-h5.ts wqbH5Status before qsStringify newQuery:', JSON.stringify(newQuery))
    // 使用自定义编码器，保持 + 符号不被编码为 %20
    const queryString = qsStringify(newQuery, { encoder: customUrlEncoder })
    console.log(`api-h5.ts wqbH5Status after qsStringify: ${queryString}`)
    callbackUrlParse.set('query', '?' + queryString)
  }
  // 如果原始链接中带有# hash数据，则应该移除，并警告
  if (callbackUrlParse.hash) {
    warn('login-wqbH5Status 回调地址带有#，已强制移除，原地址：', callbackUrlParse.href)
    woReport('login-wqbH5Status 回调地址带有#，已强制移除，原地址：', callbackUrlParse.href)
    callbackUrlParse.set('hash', '')
    warn('login-wqbH5Status 回调地址带有#，已强制移除，新地址：', callbackUrlParse.href)
  }

  // 以下渠道，每次都要要求后端刷新 session 缓存
  const updateRptidSourceList = ['app_sjyyt', '291', '296', '301']

  let newSource = loginParams.source || 'h5_mz'

  // 不是手厅、沃钱包环境，禁止使用 app_sjyyt app_wqb，如果配置了禁止参数，则强制改为h5_mz
  if (!isUnicom && !isWopay) {
    const ignoreSourceList = ['app_sjyyt', 'app_wqb']
    if (ignoreSourceList.indexOf(newSource) >= 0) newSource = 'h5_mz'
  }

  // 1-让后端清理 session，并用新的，2-(默认)后端如果有老的 session，可以不读取新的 rptid
  let isUpdateRptid = ''
  if (rptidUpdate === RptidUpdateType.DEFAULT || !rptidUpdate) {
    isUpdateRptid = updateRptidSourceList.indexOf(loginParams.source) >= 0 ? '1' : '2'
  } else {
    // ts 不能直接写 isUpdateRptid = rptidUpdate
    // 需要将 rptidUpdate 作为key，使用类型断言确保类型安全
    isUpdateRptid = RptidUpdateType[rptidUpdate as keyof typeof RptidUpdateType] || ''
  }

  const postData: ILoginParams = {
    source: newSource,
    ticket: loginParams.ticket,
    unionSessionId: loginParams.unionSessionId,
    rptid: loginParams.rptid,
    itfAuthCookieWX: loginParams.itfAuthCookieWX,
    fwAjaxCallBackUri: encodeURIComponent(callbackUrlParse.href),
    type: loginParams.type || '',
    updateRptid: isUpdateRptid,
    loginType: opts.loginType || ''
  }

  const t1 = new Date()
  formPostLogin(WQB_H5_API_URL(projectPath), postData, { 'WOAUTH-AJAX-REQUEST': true }, {}, ([err, json]) => {
    const t2 = new Date()
    loginTimer.apiTime = t2.getTime() - t1.getTime()
    if (err) {
      log('login-wqbH5Status 获取沃钱包登录信息失败')
      woReport('login-wqbH5Status 获取沃钱包登录信息失败', postData, err)
      const data = getJumpTime(opts.customParams?.source || '', 103)
      shared.jumpHookCallback(data, () => {
        resolve({
          status: false,
          data: {
            redirectUrl: '',
            cifId: '',
            zqInfo: [],
            err: '[COMMONKIT] h5-login: get wopay login info fail.',
            errId: 103,
            netErr: true
          }
        })
      })
      return
    }
    log('login-wqbH5Status', json)
    const result: ILoginH5Resp = (json as ILoginH5RespWrapped).data ? (json as ILoginH5RespWrapped).data : (json as ILoginH5Resp)
    if (result.needRedirect === 'false') {
      // 已登录
      const data = getJumpTime(opts.customParams?.source || '', 0)
      shared.jumpHookCallback(data, () => {
        loginCifId.set(result.cifId)
        loginCifIdCache.set(result.cifId)
        loginZqInfo.set(result.g2bLoginInfo)
        resolve({
          status: true,
          data: {
            redirectUrl: result.redirectUrl,
            cifId: result.cifId,
            zqInfo: result.g2bLoginInfo,
            err: '',
            errId: 0,
            netErr: false
          }
        })
      })
    } else {
      // 未登录
      if (!result.redirectUrl) {
        log('login-wqbH5Status 接口失败，缺少 redirectUrl')
        woReport('login-wqbH5Status 接口失败，缺少 redirectUrl', postData, json)
      }
      const data = getJumpTime(opts.customParams?.source || '', 102)
      shared.jumpHookCallback(data, () => {
        resolve({
          status: false,
          data: {
            redirectUrl: result.redirectUrl,
            cifId: '',
            zqInfo: [],
            err: '[COMMONKIT] wopay-login: login failed via rptid.',
            errId: 102,
            netErr: false
          }
        })
      })
    }
  })
})

/**
 * 沃钱包H5(M站)登录
 * @param opts
 */
export const wqbH5Login = (opts: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  log('login-wqbH5Login')
  wqbH5Status(opts).then((json: ILoginInnerResp) => {
    if (json.status) {
      // 已登录
      log('login-wqbH5Login 已登录')
      resolve(json)
    } else {
      // 未登录
      log('login-wqbH5Login 未登录')
      const url = json.data && json.data.redirectUrl
      if (url) {
        const data = recordJumpTime(opts.customParams?.source || '', -1)
        shared.jumpHookCallback(data, () => {
          const redirectUrlOrigin = UrlParse(url).origin
          const curUrlOrigin = window.location.origin
          if (opts.useHistoryReplace && redirectUrlOrigin === curUrlOrigin) {
            // TODO 临时去掉跳转
            // window.history.replaceState(null, '', url)
            // window.location.reload()
          } else {
            // TODO 临时去掉跳转
            // window.location.href = url
          }
        })
      } else {
        log('login-wqbH5Login 接口失败，缺少 redirectUrl')
        woReport('login-wqbH5Login 接口失败，缺少 redirectUrl', json)
        resolve({
          status: false,
          data: {
            redirectUrl: '',
            cifId: '',
            zqInfo: [],
            err: '[COMMONKIT] h5-login: API failed. No redirectUrl.',
            errId: 101,
            netErr: false
          }
        })
      }
    }
  })
})
