import { INavigationPerf, IPaintPerf, ILcpPaintPerf } from '../types'

/**
 * 毫秒时间转换，最多两位小鼠
 * @param n
 * @constructor
 */
const T = (n: number | string) => {
  const num = Number(n) || 0
  if (String(num).indexOf('.') >= 0) {
    return Number(num.toFixed(2))
  } else {
    return Number(num)
  }
}

export const navigationPerf: INavigationPerf = () => {
  const ret = {
    // 重定向时间
    redirectTime: -1,
    // DNS 查询时间
    domainLookupTime: -1,
    // TCP 连接时间
    connectTime: -1,
    // 请求响应时间
    requestTime: -1,
    // DOM 解析时间
    domContentLoadedTime: -1,
    // 加载事件时间
    loadEventTime: -1,
    // 页面总加载时间
    durationTime: -1
  }

  if (window.performance) {
    const entries = window.performance.getEntries() as PerformanceEntry[]
    const navigation = entries.filter(entry => entry.entryType === 'navigation') as PerformanceNavigationTiming[]
    // 如果调用过早，可能无法获取 paint 数据，故要改为 PerformanceObserver 形式获取
    // const paint = entries.filter(entry => entry.entryType === 'paint') as PerformancePaintTiming[]
    // const resource = entries.filter(entry => entry.entryType === 'resource') as PerformanceResourceTiming[]
    // const visibility = entries.filter(entry => entry.entryType === 'visibility-state')

    if (navigation && navigation[0]) {
      const navigationTiming = navigation[0]
      // 重定向时间
      ret.redirectTime = T(navigationTiming.redirectEnd - navigationTiming.redirectStart)
      // DNS 查询时间
      ret.domainLookupTime = T(navigationTiming.domainLookupEnd - navigationTiming.domainLookupStart)
      // TCP 连接时间
      ret.connectTime = T(navigationTiming.connectEnd - navigationTiming.connectStart)
      // 请求响应时间
      ret.requestTime = T(navigationTiming.responseEnd - navigationTiming.requestStart)
      // DOM 解析时间
      ret.domContentLoadedTime = T(navigationTiming.domInteractive - navigationTiming.domContentLoadedEventEnd)
      // 加载事件时间
      ret.loadEventTime = T(navigationTiming.loadEventEnd - navigationTiming.loadEventStart)
      // 页面总加载时间
      ret.durationTime = T(navigationTiming.duration)
    }
    // if (paint) {
    //   const firstPaint = paint.filter(item => item.name === 'first-paint')[0]
    //   const firstContentfulPaint = paint.filter(item => item.name === 'first-contentful-paint')[0]
    //   // FP
    //   if (firstPaint) ret.fpTime = T(firstPaint.startTime)
    //   // FCP
    //   if (firstContentfulPaint) ret.fcpTime = T(firstContentfulPaint.startTime)
    // }
  }
  return ret
}

export const paintPerf: IPaintPerf = (callback) => {
  const ret = {
    // FP
    fpTime: -1,
    // FCP
    fcpTime: -1
  }

  if (PerformanceObserver) {
    const perfObserver = new PerformanceObserver(list => {
      list.getEntries().forEach(entry => {
        if (entry.name === 'first-paint') {
          ret.fpTime = T(entry.startTime)
        } else if (entry.name === 'first-contentful-paint') {
          ret.fcpTime = T(entry.startTime)
        }
      })
      callback(ret)
      perfObserver.disconnect() // 仅测量一次
    })
    perfObserver.observe({ type: 'paint', buffered: true })
  } else {
    callback(ret)
  }
}

export const lcpPaintPerf: ILcpPaintPerf = (callback) => {
  const ret = {
    // LCP
    lcpTime: -1
  }
  if (PerformanceObserver) {
    const perfObserver = new PerformanceObserver(list => {
      list.getEntries().forEach(entry => {
        if (entry.entryType === 'largest-contentful-paint') {
          ret.lcpTime = T(entry.startTime)
        }
      })
      callback(ret)
      perfObserver.disconnect() // 仅测量一次
    })
    perfObserver.observe({ type: 'largest-contentful-paint', buffered: true })
  } else {
    callback(ret)
  }
}
