import { Location } from 'vue-router'
import { Route } from 'vue-router/types/router'
import { log } from '../../_tools/log'
import { loadMs, ms } from '../../UnicomClientApi'
import { isUnicom } from '../../env'

// 处理 vue-router promiseError 报错问题
export const vueRouterFix = (VueRouter: any) => {
  const originalPush = VueRouter.prototype.push
  VueRouter.prototype.push = function push (location: Location, onResolve: Function, onReject: (err: Error) => void) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch((err: any) => err)
  }
  const originalReplace = VueRouter.prototype.replace
  VueRouter.prototype.replace = function replace (location: Location, onResolve: Function, onReject: (err: Error) => void) {
    if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
    return originalReplace.call(this, location).catch((err: any) => err)
  }
}

/**
 * 设置页面标题（通过meta.title字段）
 * @param to
 * @param defaultTitle
 */
export const afterEachSetTitle = (to: Route, defaultTitle: string = '') => {
  let title = defaultTitle
  if (to.meta && (to.meta.title !== undefined && to.meta.title !== null)) title = to.meta.title
  log('afterEachSetTitle', title)
  window.document.title = title
  if (isUnicom) {
    loadMs().then(() => {
      ms.setNavigationBarTitle(title)
    })
  }
}

/**
 * 处理滚动条位置
 */
export const afterEachScroll = () => {
  // 页面回到顶部
  window.scrollTo(0, 0)
  // 解决某些情况下（特别是后退）页面显示不全的问题
  setTimeout(() => {
    document.documentElement.scrollTop = document.documentElement.scrollTop - 1
    document.body.scrollTop = document.body.scrollTop - 1
  }, 500)
}
