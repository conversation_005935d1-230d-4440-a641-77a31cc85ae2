/**
 * 内部日志管理
 */
export const logger = {
  data: ([]) as any[],
  func: (() => {}) as Function,
  set (args: any) {
    args.unshift(new Date().toJSON())
    this.data.push(args)
    this.func && this.func(this.data)
  },
  get () {
    return this.data
  },
  bind (fn: Function) {
    this.func = fn
  }
}

/**
 * COMMONKIT 内部日志
 * @param args
 */
export function log (...args: any[]) {
  args.unshift('[CommonKit]')
  console.log.apply(console, args)
  logger.set(args)
}

/**
 * COMMONKIT 内部日志
 * @param args
 */
export function warn (...args: any[]) {
  args.unshift('[CommonKit]')
  console.warn.apply(console, args)
  logger.set(args)
}

/**
 * COMMONKIT 内部日志
 * @param args
 */
export function error (...args: any[]) {
  args.unshift('[CommonKit]')
  console.error.apply(console, args)
  logger.set(args)
}
