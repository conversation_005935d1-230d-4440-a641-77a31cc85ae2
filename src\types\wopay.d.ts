import { IObj } from './global'

declare global {
  interface Window {
    WoPay: any
  }
}

export interface IWoPay {
  login: any //登录
  doApplePay: any //苹果支付
  jdMotionLiveness: any //京东活体
  motionLiveness: any //商汤活体
  exRpt: any //获取rptId
  getBaseInfo: any //获取客户端基础信息
  sendSMS: any //发送短信
  getLocation: any //获取定位信息
  OCR: any //获取OCR信息
  merchantQR: any //保存二维码
  module: any //拉起客户端功能模块
  smartDetect: any //扫码获取信息
  feedback: any //用户反馈
  digitalSignature: any //数字证书
  fingerPrintAuth: any //指纹支付
  newPage: any //打开新页面
  isQQInstall: any //是否安装QQ
  interact: any //获取用户信息
  refresh: any //刷新页面
  rollback: any //返回钱包页
  dismiss: any //返回上一页
  camera: any //拉起相机
  photos: any //拉起相册
  contacts: any //获取联系人
  realNameAuth: any //拉起实名页面
  bindCard: any //拉起绑卡页面
  share: any //拉起分享页面
  notifyEvents: any
  backredirect: any // 后退按钮控制
  refreshUserInfo: any  //刷新用户实名信息
  getDevicelnfo: any //获取设备ID
  getTokenWalletContextId // token 钱包相关API
  initToken // token 钱包相关API
  appletTokenOpen // token 钱包相关API
  openChannel // token 钱包相关API
  executeApdu // token 钱包相关API
  executeApduList // token 钱包相关API
  closeChannel // token 钱包相关API

  // 历史 API
  rightBar: any // 右上角按钮控制
}

export interface IWopayBaseInfo {
  deviceInfo: IObj
}

export interface IWopayLocationInfo {
  resultCode: string
  latitude: number
  longitude: number
}

export interface IWopayRightBtnOptions {
  text: string
}

export interface IWopayOpenPageOptions {
  url: string
}

export interface IWopayRightButtonFunc {
  fn: Function | null
  fn2: Function | null
}
