// sessionStorage 缓存数据字段名
import { storage } from '../utils'

// 落地页存储
const SHARE_LANDING_URL_KEY = 'COMMONKIT_SHARE_LANDING_URL'
export const landingUrl = storage(SHARE_LANDING_URL_KEY)

const isProdFn = () => {
  const host = window.location.host
  return host === 'epay.10010.com'
}

// 判断是否为生产环境
export const isProd = isProdFn()

// 微信分享APPID-生产环境
const WX_APPID_PROD = 'wx22a6154dfb9e6b4f'
// 微信分享APPID-测试环境
const WX_APPID_DEV = 'wxf304ed38ce206568'
// 微信分享APPID
export const WX_APPID = isProd ? WX_APPID_PROD : WX_APPID_DEV
