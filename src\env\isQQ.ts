import { ua } from './utils'

const checkQQ = (ua: string) => /QQ/i.test(ua)
const checkTim = (ua: string) => /Tim/i.test(ua)
const checkQQInternational = (ua: string) => /QQInternational/i.test(ua)
const verQQ = (ua: string) => ua.match(/QQ\/([\d+.]+)/i) || ''
const verTim = (ua: string) => ua.match(/TIM\/([\d+.]+)/i) || ''
const ver = (ua: string) => isTim ? verTim(ua) : verQQ(ua)

export const isTim = checkTim(ua)
export const isQQInternational = checkQQInternational(ua)
export const isQQ = !isQQInternational && !isTim && checkQQ(ua)
export const qqVer = ver(ua) ? ver(ua)[1] : ''
