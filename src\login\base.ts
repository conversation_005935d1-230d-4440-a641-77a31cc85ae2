import { isUnicom, isWopay } from '../env'
import { wqbNewAppLogin, wqbNewAppStatus } from './api-wopay'
import { unicomStatus, unicomLogin } from './api-unicom'
import { wqbH5Status, wqbH5Login } from './api-h5'
import { LoginType, WopayLoginType } from './enum'
import { ILoginOptions } from '../types'

/**
 * 登录状态验证
 * @param projectPath
 * @param opts
 */
export const status = (projectPath: string, opts: ILoginOptions = {}) => {
  console.log(`base.ts status ${window.location.href}`)
  const apiLoginPath = window.COMMONKIT_LOGIN_PATH
  const defaultOpts: ILoginOptions = {
    projectPath: projectPath || apiLoginPath,
    wopayLoginMode: WopayLoginType.DEFAULT,
    wopayRptTimeout: 3,
    loginType: LoginType.DEFAULT
  }
  const options: ILoginOptions = Object.assign({}, defaultOpts, opts)
  // 新版沃钱包APP(430之后)
  if (isWopay) return wqbNewAppStatus(options)

  // 手厅APP
  if (isUnicom) return unicomStatus(options)

  // 其他渠道（走沃钱包H5）
  return wqbH5Status(options)
}

/**
 * 登录
 * @param projectPath
 * @param opts
 */
export const login = (projectPath: string, opts: ILoginOptions = {}) => {
  const apiLoginPath = window.COMMONKIT_LOGIN_PATH
  const defaultOpts: ILoginOptions = {
    projectPath: projectPath || apiLoginPath,
    callbackUrl: window.location.href,
    isWqbAppReloadPage: true,
    useHistoryReplace: false,
    wopayLoginMode: WopayLoginType.DEFAULT,
    wopayRptTimeout: 3,
    loginType: LoginType.DEFAULT
  }
  console.log(`base.ts login ${defaultOpts.callbackUrl}`)
  const options: ILoginOptions = Object.assign({}, defaultOpts, opts)

  // 新版沃钱包APP(430之后)
  if (isWopay) return wqbNewAppLogin(options).then(json => {
    if (json.status) {
      if (options.isWqbAppReloadPage) window.location.reload()
    }
    return json
  })

  // 手厅APP
  if (isUnicom) return unicomLogin(options)

  // 其他渠道（走沃钱包H5）
  return wqbH5Login(options)
}
