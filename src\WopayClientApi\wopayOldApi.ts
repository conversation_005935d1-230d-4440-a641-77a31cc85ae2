import WopayClientApi from './wopay'
import {
  IWopayBaseInfo,
  IWopayLocationInfo,
  IWopayRightBtnOptions,
  IWopayOpenPageOptions,
  IWopayRightButtonFunc
} from '../types'

// 设备信息操作过程中不会发生改变，可以全局缓存
const deviceData = {
  osType: -1, // 系统类型 -1-未知, 1-iOS, 2-Android
  osVersion: '', // 系统版本号
  manufacturer: '', // 设备制造商
  model: '', // 设备型号
  ip: '', // IP
  network: -1, // 网络类型: -1-无网络, 0-WIFI, 1-未知的移动网络, 2-2G, 3-3G, 4-4G,
  uuid: '', // 设备唯一识别号（安卓机器同imei字段）
  idfa: '', // iOS 专有（这个字段 sdk 貌似获取不到）
  idfv: '', // iOS 专有
  mac: '', // Android 专有，iOS获取不到（安卓可能获取是假的值）
  imei: '', // Android 专有，iOS获取不到
  androidId: '', // Android 专有
  meid: '' // Android 专有（同imei字段）
}

export const wopayDeviceInfo = (callback: Function | null) => {
  WopayClientApi.getBaseInfo({
    success: function (res: IWopayBaseInfo) {
      const device = res.deviceInfo
      deviceData.manufacturer = device.manufacturer || ''
      deviceData.model = device.model || ''
      switch (device.deviceType) {
        case '1':
          // iOS
          deviceData.osType = 1
          break
        case '2':
          // iOS
          deviceData.osType = 2
          break
        default:
          deviceData.osType = 1
      }
      // deviceversion-ios, osVersion-android
      deviceData.osVersion = device.deviceversion || device.osVersion || ''
      // 沃钱包只能获取启动APP时候的网络情况（非实时）
      switch (device.netType) {
        // Wifi-ios， WIFI-android
        case 'Wifi':
        case 'WIFI':
          deviceData.network = 0
          break
        case '2G':
          deviceData.network = 2
          break
        case '3G':
          deviceData.network = 3
          break
        case '4G':
          deviceData.network = 4
          break
        default:
          deviceData.network = -1
      }
      deviceData.ip = device.ip || ''
      deviceData.uuid = device.uuid || ''
      // iOS
      deviceData.idfa = device.idfa || ''
      deviceData.idfv = device.idfv || ''
      // android
      deviceData.imei = device.imei || ''
      deviceData.mac = device.mac || ''
      deviceData.androidId = device.androidId || ''
      deviceData.meid = device.meid || ''
      callback && callback(null, deviceData)
    }
  })
}

let LocationData = {
  latitude: 0,
  longitude: 0
}

export const wopayLocationInfo = (callback?: Function, forceRefresh?: boolean) => {
  forceRefresh = typeof forceRefresh === 'boolean' ? forceRefresh : true
  if (forceRefresh) {
    // 清空缓存
    LocationData = {
      latitude: 0,
      longitude: 0
    }
  }
  WopayClientApi.getLocation({
    appId: '100000',
    appName: '沃钱包',
    address: true,
    success: function (res: IWopayLocationInfo) {
      if (res.resultCode === '1') {
        LocationData.latitude = res.latitude
        LocationData.longitude = res.longitude
      }
      callback && callback(null, LocationData)
    },
    failed: function () {
      callback && callback(null, LocationData)
    }
  })
}

export const wopayRightButton = (options: IWopayRightBtnOptions, callback?: Function) => {
  const F: IWopayRightButtonFunc = {
    fn: null,
    fn2: null
  }
  F.fn = (isWindowLoaded: boolean) => {
    if (isWindowLoaded) {
      // @ts-ignore
      window.removeEventListener('load', F.fn2)
    }
    WopayClientApi.rightBar({
      hide: false,
      display: [
        {
          title: options.text,
          success: function () {
            callback && callback(null)
          }
        }]
    })
  }
  F.fn2 = F.fn.bind(null, true)
  // 防止页面打开后，存在异步获取 script
  // 此时会将右上角按钮恢复成原有样式
  // 故需要在 onload 中再次覆盖
  // @ts-ignore
  window.addEventListener('load', F.fn2)
  F.fn()
}

export const wopayOpenPage = (options: IWopayOpenPageOptions, callback?: Function) => {
  const url = (options && options.url) || ''
  WopayClientApi.newPage({ url, reqMethod: 'GET' })
}

export const wopayClosePage = (options: IWopayOpenPageOptions, callback?: Function) => {
  WopayClientApi.dismiss()
}
