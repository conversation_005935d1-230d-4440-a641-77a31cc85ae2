import { ua } from './utils'

const checkWx = (ua: string) => /MicroMessenger/i.test(ua)
const checkWxWork = (ua: string) => /wxwork/i.test(ua)
const verWx = (ua: string) => ua.match(/MicroMessenger\/([\d+.]+)/i) || ''
const verWxWork = (ua: string) => ua.match(/wxwork\/([\d+.]+)/i) || ''
const ver = (ua: string) => isWeixin ? verWx(ua) : verWxWork(ua)

export const isWeixinLike = checkWx(ua)

export const isWeixinWork = checkWxWork(ua)

export const isWeixin = isWeixinLike && !isWeixinWork

export const weixinVer = ver(ua) ? ver(ua)[1] : ''
