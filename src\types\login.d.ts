import Vue from 'vue'
import { NavigationGuardNext, Route } from 'vue-router/types/router'
import { RptidUpdateType } from '../login'
import { LoginType } from '../login/enum'
import { IObj } from './global'

declare global {
  interface Window {
    COMMONKIT_WOPAY_CLIENT_ID: string
    COMMONKIT_LOGIN_PATH: string
  }
}

export interface ILoginParams {
  source: string
  channelCode?: string
  ticket?: string
  unionSessionId?: string
  rptid?: string
  itfAuthCookieWX?: string
  fwAjaxCallBackUri?: string
  type?: string
  updateRptid?: string
  extraTxt?: string
  loginType?: keyof LoginType
}

export interface IRemoveLoginParams extends IObj {
  ticket?: string
  unionSessionId?: string
  rptid?: string
  rpt?: string
}

export interface ILoginOptions {
  projectPath?: string
  callbackUrl?: string
  isWqbAppReloadPage?: boolean
  useHistoryReplace?: boolean
  wopayLoginMode?: number
  wopayRptTimeout?: number
  customParams?: ILoginParams
  rptidUpdate?: keyof RptidUpdateType
  loginType?: keyof LoginType
}

export interface ILoginInnerRespData {
  redirectUrl: string
  cifId: string
  zqInfo: IObj
  err: string
  errId: number
  netErr: boolean
}

export interface ILoginInnerResp {
  status: boolean
  data: ILoginInnerRespData
}

export interface ILoginH5RespWrapped {
  code: string
  data: ILoginH5Resp
}

export interface ILoginH5Resp {
  needRedirect: string
  redirectUrl: string
  cifId: string
  g2bLoginInfo: IObj
}

export interface ILoginWopayRptResp {
  resultCode: string
  rpt: string
  principalId: string
}

export interface IVueHelperLoginOptions {
  notCheckLogin: boolean
  replace: boolean
  isLocal: boolean
  callbackUrl: string
}

export interface IVueHelperLoginRunOptions {
  autoQueryStatus: boolean
}

export interface IVueHelperLoginState {
  status: number
  cifId: string
  err: string
  errId: number
  zqInfo: IObj
}

export interface IVueHelperQueryStatus {
  (opts?: ILoginOptions): Promise<Boolean>
}

export interface IVueHelperToLoginPayloadCallback {
  (state: number): void
}

export interface IVueHelperToLoginPayload {
  callback?: IVueHelperToLoginPayloadCallback
  loginType?: string
  isLocal?: boolean
  callbackUrl?: string
}

export interface IVueHelperToLogin {
  (payload?: IVueHelperToLoginPayload, options?: ILoginOptions): void
}

export interface IVueHelperUseLogin {
  (): [IVueHelperLoginState, IVueHelperQueryStatus, IVueHelperToLogin]
}

export interface IVueRouterGuard<V extends Vue = Vue> {
  to: Route
  from: Route
  next: NavigationGuardNext<V>
  loginRouteName: string
  autoQueryStatus?: boolean
  loginType?: keyof LoginType
  handler?: Function
}

export interface IVueHelperInterceptorNext {
  (status: number, params?: any): void
}

// -1-去跳转登录，0-登录成功，102-登录失败（无有效响应），103-登录接口失败
type LoginJumpUrlHookDataType = -1 | 0 | 102 | 103

export interface ILoginJumpUrlHookData {
  viaLogin: boolean
  type: LoginJumpUrlHookDataType
  time: number
  diff: number
  source: string
}

export interface ILoginJumpUrlHook {
  (callback: (data: ILoginJumpUrlHookData, next: Function) => void): void
}

export interface ILoginJumpUrlHookJumpTimeFn {
  (source: string, type: LoginJumpUrlHookDataType): ILoginJumpUrlHookData
}
