import { ENV_APP_NAME } from './envAppName'
import { jsonPost } from './request'
import { storage } from './storage'
import { loginCifId, loginCifIdCache } from '../login/config'

const uidStorage = storage('COMMONKIT_UID', true)
const sidStorage = storage('COMMONKIT_SID')

/**
 * 获取当前时间
 * 格式如：2023-12-12 15:12:34
 */
const getDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = ('0' + (now.getMonth() + 1)).slice(-2)
  const day = ('0' + now.getDate()).slice(-2)
  const hours = ('0' + now.getHours()).slice(-2)
  const minutes = ('0' + now.getMinutes()).slice(-2)
  const seconds = ('0' + now.getSeconds()).slice(-2)
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

const eid = () => {
  const id = loginCifId.get() || loginCifIdCache.get() // 优先获取当前线程的，获取不到读取缓存之前的
  if (id) {
    return '(CID=' + Number(id.slice(-8)).toString(36) + ')'
  } else {
    return ''
  }
}

// eid 反向函数
// const eidRe = (str) => {
//   const num = parseInt(str, 36)
//   return num.toString().padStart(8, '0')
// }

/**
 * 生成(获取)唯一12位用户识别码
 */
const uid = () => {
  const cache = uidStorage.get()
  if (cache) return cache
  const s = Math.random().toString().slice(2, 14)
  uidStorage.set(s)
  return s
}

/**
 * 生成(获取)唯一12位流水号
 */
const sid = () => {
  const cache = sidStorage.get()
  if (cache) return cache
  const s = Math.random().toString().slice(2, 14)
  sidStorage.set(s)
  return s
}

const stringify = (data: any) => {
  let ret
  try {
    ret = JSON.stringify(data)
  } catch (e) {
    ret = 'JSON_STRINGIFY 失败'
  }
  return ret
}

/**
 * 日志时间格式化
 * 输出如 UID | SID | 2023-12-12 15:12:34 | ITF-XX-XXXX-WEB | {xxx:xxx} | URL=XXXX | USERAGENT=XXXX
 * @param args
 */
const reporterFormat = (...args: any[]) => {
  const arr = []
  arr.push(uid() + eid())
  arr.push(sid())
  arr.push(getDateTime())
  arr.push(ENV_APP_NAME())
  arr.push(stringify(args))
  arr.push('URL=' + window.location.href)
  arr.push('USERAGENT=' + window.navigator.userAgent)
  return arr.join(' | ')
}

/**
 * 自有错误上报
 * @param args
 */
export function woReport (...args: any[]) {
  const url = '/itf/bscorefront/api/log/record'
  const postData = { data: reporterFormat(...args) }
  setTimeout(() => {
    // 延迟上报，避免上报时页面卡顿
    jsonPost(url, postData, { Cookie: '' }, { withCredentials: false }).then(() => {}).catch(() => {})
  })
}
