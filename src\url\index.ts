import { parse, stringify } from 'qs'
import UrlParse from 'url-parse'
import { IObj } from '../types'

/**
 * 自定义 URL 编码器，保持 + 符号不被编码为 %20
 * @param str 要编码的字符串
 * @returns 编码后的字符串
 */
export const customUrlEncoder = (str: string): string => {
  // 先进行标准的 URL 编码
  const encoded = encodeURIComponent(str)
  // 然后将 %20 (空格) 替换回 +，保持 + 符号原样
  return encoded.replace(/%20/g, '+')
}

/**
 * 解析 url
 * @param url
 * @returns any
 */
export const urlParse = (url: string): any => new UrlParse(url)

/**
 * url中追加参数
 * @param path
 * @param queryString
 * @returns {string}
 */
export const urlAppend = (path: string, queryString: IObj): string => {
  const { origin, pathname, query, hash } = new UrlParse(path)
  console.log(`index.ts urlAppend ${query.substring(1)}`)
  const pathParams = parse(query.substring(1), { decoder: str => decodeURIComponent(str) })
  const params = Object.assign({}, pathParams, queryString)
  console.log('url/index.ts urlAppend params:', JSON.stringify(params))
  const url = origin + pathname
  // 使用自定义编码器，保持 + 符号不被编码为 %20
  const queryStr = stringify(params, { encoder: customUrlEncoder })
  console.log(`url/index.ts urlAppend final queryStr: ${queryStr}`)
  return url + '?' + queryStr + hash
}

/**
 * 获取url中原始参数
 * @returns {*}
 */
export const getUrlParams = (): IObj => {
  const search = window.location.search
  console.log(`url/index.ts getUrlParams search: ${search}`)
  const result = parse(search.substring(1), { decoder: str => decodeURIComponent(str) })
  console.log('url/index.ts getUrlParams result:', JSON.stringify(result))
  return result
}

export { parse as qsParse, stringify as qsStringify }
