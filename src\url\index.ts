import { parse, stringify } from 'qs'
import UrlParse from 'url-parse'
import { IObj } from '../types'

/**
 * 解析 url
 * @param url
 * @returns any
 */
export const urlParse = (url: string): any => new UrlParse(url)

/**
 * url中追加参数
 * @param path
 * @param queryString
 * @returns {string}
 */
export const urlAppend = (path: string, queryString: IObj): string => {
  const { origin, pathname, query, hash } = new UrlParse(path)
  console.log(`index.ts urlAppend ${query.substring(1)}`)
  const pathParams = parse(query.substring(1), { decoder: str => decodeURIComponent(str) })
  const params = Object.assign({}, pathParams, queryString)
  console.log('url/index.ts urlAppend params:', JSON.stringify(params))
  const url = origin + pathname
  const queryStr = stringify(params)
  console.log(`url/index.ts urlAppend final queryStr: ${queryStr}`)
  return url + '?' + queryStr + hash
}

/**
 * 获取url中原始参数
 * @returns {*}
 */
export const getUrlParams = (): IObj => {
  const search = window.location.search
  console.log(`url/index.ts getUrlParams search: ${search}`)
  const result = parse(search.substring(1), { decoder: str => decodeURIComponent(str) })
  console.log('url/index.ts getUrlParams result:', JSON.stringify(result))
  return result
}

export { parse as qsParse, stringify as qsStringify }
