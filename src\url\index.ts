import { parse, stringify } from 'qs'
import UrlParse from 'url-parse'
import { IObj } from '../types'

/**
 * 解析 url
 * @param url
 * @returns any
 */
export const urlParse = (url: string): any => new UrlParse(url)

/**
 * url中追加参数
 * @param path
 * @param queryString
 * @returns {string}
 */
export const urlAppend = (path: string, queryString: IObj): string => {
  const { origin, pathname, query, hash } = new UrlParse(path)
  console.log(`index.ts urlAppend ${query.substring(1)}`)
  const pathParams = parse(query.substring(1), { decoder: str => decodeURIComponent(str) })
  const params = Object.assign({}, pathParams, queryString)
  const url = origin + pathname
  return url + '?' + stringify(params) + hash
}

/**
 * 获取url中原始参数
 * @returns {*}
 */
export const getUrlParams = (): IObj => {
  const search = window.location.search
  return parse(search.substring(1), { decoder: str => decodeURIComponent(str) })
}

export { parse as qsParse, stringify as qsStringify }
