import { log } from '../_tools/log'
import { woReport } from '../_tools/reporter'
import { navigationPerf, paintPerf, lcpPaintPerf } from './perf'
import { IAnalyticsPerf } from '../types'

export const analyticsRun: IAnalyticsPerf = (callback) => {
  try {
    const navigation = navigationPerf()
    woReport('性能统计：', navigation)
    log(`性能-重定向: ${navigation.redirectTime} ms`)
    log(`性能-DNS查询: ${navigation.domainLookupTime} ms`)
    log(`性能-TCP连接: ${navigation.connectTime} ms`)
    log(`性能-请求响应: ${navigation.requestTime} ms`)
    log(`性能-DOM 解析: ${navigation.domContentLoadedTime} ms`)
    log(`性能-加载事件: ${navigation.loadEventTime} ms`)
    log(`性能-页面总加载: ${navigation.durationTime} ms`)
    callback && callback({ type: 'navigation', data: navigation })

    paintPerf(paint => {
      if (paint.fpTime !== -1 || paint.fcpTime !== -1) {
        woReport('性能统计：', `FP: ${paint.fpTime} ms, FCP:${paint.fcpTime} ms`)
      }
      log(`性能-FP: ${paint.fpTime} ms, FCP:${paint.fcpTime} ms`)
      callback && callback({ type: 'paint', data: paint })
    })

    lcpPaintPerf(lcpPaint => {
      if (lcpPaint.lcpTime !== -1) {
        woReport('性能统计：', `LCP: ${lcpPaint.lcpTime} ms`)
      }
      log(`性能-LCP: ${lcpPaint.lcpTime} ms`)
      callback && callback({ type: 'lcp-paint', data: lcpPaint })
    })
  } catch (e) {
    // 不做处理
  }
}
