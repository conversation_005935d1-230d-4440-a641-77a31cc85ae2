!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.Compile=n():e.Compile=n()}(window,(function(){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var a=n[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,t),a.l=!0,a.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var a in e)t.d(r,a,function(n){return e[n]}.bind(null,a));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=146)}([function(e,n,t){"use strict";t(27),t(4),t(120),t(2),t(37),t(90),t(31),t(47),t(68);var r=t(1),a={isInApp:function(){return navigator.userAgent.indexOf("unicom")>-1||!!a.isVersionGreaterThanOrEqualTo10_2()},isIOS:function(){var e=navigator.userAgent.toLowerCase();return/iphone|ipad|ipod|macintosh/.test(e)},isAndroid:function(){var e=navigator.userAgent,n=e.indexOf("Android")>-1||e.indexOf("Adr")>-1;if(0==n&&a.isVersionGreaterThanOrEqualTo10_2())return"android"==window.ChinaUnicomAppPhoneInformation.CUVersion.split("@")[0];return n},isHarmony:function(){var e=navigator.userAgent;return/ArkWeb|OpenHarmony|HarmonyOS/.test(e)},appVersion:function(){if(a.isVersionGreaterThanOrEqualTo10_2())return window.ChinaUnicomAppPhoneInformation.CUVersion.split("@")[1];var e=window.navigator.userAgent.match(/unicom{version:([^},)]+)+/);if(e&&e.length>1){var n=e[1];return n.split("@")[1]}var t=this.getCookieFromDocument("c_version");return t?t.split("@")[1]:(console.warn("获取不到版本号，默认返回8.0000"),8)},isVersionGreaterThanOrEqualTo10_2:function(){if((window.AndroidChinaUnicomAppPhoneInformation&&window.AndroidChinaUnicomAppPhoneInformation.load&&"function"==typeof window.AndroidChinaUnicomAppPhoneInformation.load&&(window.ChinaUnicomAppPhoneInformation=JSON.parse(window.AndroidChinaUnicomAppPhoneInformation.load())),null!=window.ChinaUnicomAppPhoneInformation)&&window.ChinaUnicomAppPhoneInformation.CUVersion.split("@")[1]>=10.02)return!0;return!1},isEdop:function(){if(a.isInApp()&&a.appVersion()>=9.05){var e=r.a.execSync("isEdop");return e}var n=window.sessionStorage.getItem("isEdop");return!!(window.location.href.startsWith("file:")||window.location.href.indexOf("edopDebug=edopDebug")>0||n&&"edop"==n)&&(window.sessionStorage.setItem("isEdop","edop"),!0)},execIOSJSBridge:function(e){var n=document.createElement("iframe");n.src=e,n.style.display="none",document.body.appendChild(n),window.setTimeout((function(){document.body.removeChild(n)}),1e3)},getCookieFromDocument:function(e){var n,t=new RegExp("(^| )"+e+"=([^;]*)(;|$)");if(n=document.cookie.match(t))return unescape(n[2]);var r=document.URL;r=(r=r.substring(r.indexOf("?"))).replace(/#\//gi,"&").replace(/\?/gi,"&"),t=new RegExp("/".concat(e,"=([^&]+)($|[^&])/"));var a=r.match(t);return a?unescape(a[2]):null},generateUUID:function(){var e=(new Date).getTime();return window.performance&&"function"==typeof window.performance.now&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(n){var t=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==n?t:3&t|8).toString(16)}))}};n.a=a},function(e,n,t){"use strict";t(22),t(73),t(4),t(2),t(37),t(23);var r=t(0),a=(t(27),t(31),{_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var n,t,r,o,i,s,c,u="",l=0;for(e=a._utf8_encode(e);l<e.length;)o=(n=e.charCodeAt(l++))>>2,i=(3&n)<<4|(t=e.charCodeAt(l++))>>4,s=(15&t)<<2|(r=e.charCodeAt(l++))>>6,c=63&r,isNaN(t)?s=c=64:isNaN(r)&&(c=64),u=u+this._keyStr.charAt(o)+this._keyStr.charAt(i)+this._keyStr.charAt(s)+this._keyStr.charAt(c);return u},decode:function(e){var n,t,r,o,i,s,c="",u=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");u<e.length;)n=this._keyStr.indexOf(e.charAt(u++))<<2|(o=this._keyStr.indexOf(e.charAt(u++)))>>4,t=(15&o)<<4|(i=this._keyStr.indexOf(e.charAt(u++)))>>2,r=(3&i)<<6|(s=this._keyStr.indexOf(e.charAt(u++))),c+=String.fromCharCode(n),64!=i&&(c+=String.fromCharCode(t)),64!=s&&(c+=String.fromCharCode(r));return c=a._utf8_decode(c)},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");for(var n="",t=0;t<e.length;t++){var r=e.charCodeAt(t);r<128?n+=String.fromCharCode(r):r>127&&r<2048?(n+=String.fromCharCode(r>>6|192),n+=String.fromCharCode(63&r|128)):(n+=String.fromCharCode(r>>12|224),n+=String.fromCharCode(r>>6&63|128),n+=String.fromCharCode(63&r|128))}return n},_utf8_decode:function(e){for(var n="",t=0,r=0,a=0,o=0;t<e.length;)(r=e.charCodeAt(t))<128?(n+=String.fromCharCode(r),t++):r>191&&r<224?(a=e.charCodeAt(t+1),n+=String.fromCharCode((31&r)<<6|63&a),t+=2):(a=e.charCodeAt(t+1),o=e.charCodeAt(t+2),n+=String.fromCharCode((15&r)<<12|(63&a)<<6|63&o),t+=3);return n}}),o=a;window.msMessageCallback||(window.msMessageCallback=function(e){e.data&&e.data.type&&"MessageFromNative"==e.data.type&&(console.log("window.msMessageCallback",e),i.callbackFromNative(e.data.message))}),window.removeEventListener("message",window.msMessageCallback),window.addEventListener("message",window.msMessageCallback);var i=window.MsJSBridge={MsUniqueId:1,callbacks:{},eventListenerStorage:{},exec:function(e,n,t,a,o){if(r.a.isInApp()){!0!==o&&(o=!1);var s={action:e,parameter:n,isKeepAlive:o},c={};if("function"==typeof t&&(c.success=t),"function"==typeof a&&(c.fail=a),Object.keys(c).length){var u=i.getUniqueId();i.callbacks[u]=c,s.callbackId=u}var l=JSON.stringify(s);r.a.isAndroid()?AndroidMsJSBridge.exec(l):r.a.isIOS()?window.webkit.messageHandlers.MsJSBridge.postMessage(l):r.a.isHarmony()&&HarmonyMsJSBridge.exec(l)}else a("端外不支持"+e+"方法调用！")},execSync:function(e,n){if(!e)return console.debug("调用同步方法传入的action为空"),null;var t={action:e,parameter:n||""},a=JSON.stringify(t),i="";if(!r.a.isInApp())return console.warn("端外不支持（"+e+"）方法调用"),null;if(r.a.isAndroid()?i=AndroidMsJSBridge.execSync(a):r.a.isIOS()?i=window.prompt(a,"MsJSBridge"):r.a.isHarmony()&&(i=HarmonyMsJSBridge.execSync(a)),i){try{var s="";return r.a.isAndroid()||r.a.isIOS()?s=decodeURIComponent(atob(i)):r.a.isHarmony()&&(s=o.decode(i)),JSON.parse(s).data}catch(e){console.warn("MsJSBridge-execSync-报错："+e)}return null}},callbackFromNative:function(e){try{var n="";r.a.isAndroid()||r.a.isIOS()?n=decodeURIComponent(atob(e)):r.a.isHarmony()&&(n=o.decode(e));var t=JSON.parse(n),a=i.callbacks[t.callbackId];if(a){var s=t.parameter,c=t.isKeepAlive;"success"==s.status&&a&&a.success?a.success(s.data,c):"fail"==s.status&&a&&a.fail&&(c=!1,a.fail(s.data,c)),c||delete i.callbacks[t.callbackId]}else{var u=window.document.querySelectorAll("iframe");u&&u.forEach((function(n){n.contentWindow.postMessage({message:e,type:"MessageFromNative"},"*")}))}}catch(e){console.warn("MsJSBridge-callbackFromNative-报错："+e)}},addEventListener:function(e,n){var t=this.eventListenerStorage[e];t||(t={},this.eventListenerStorage[e]=t);var r=this.getUniqueId();return t[r]=n,r},removeEventListener:function(e,n){if(n){var t=this.eventListenerStorage[e];t&&delete t[n]}},receiveEventActionFromNative:function(e){try{var n="";r.a.isAndroid()||r.a.isIOS()?n=decodeURIComponent(atob(e)):r.a.isHarmony()&&(n=o.decode(e));var t=JSON.parse(n),a=t.action,i=t.parameter||"",s=this.eventListenerStorage[a];if(!s)return;for(var c in s){var u=s[c];u&&u(i)}}catch(e){console.warn("MsJSBridge-receiveEventActionFromNative-报错："+e)}},getUniqueId:function(){return i.MsUniqueId++,"MsJSBridge_"+i.MsUniqueId.toString()+"_"+(new Date).getTime()}};n.a=i},function(e,n,t){"use strict";var r=t(7),a=t(44);r({target:"RegExp",proto:!0,forced:/./.exec!==a},{exec:a})},function(e,n,t){"use strict";function r(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function o(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){r(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}t.d(n,"a",(function(){return o}))},function(e,n,t){var r=t(62),a=t(17),o=t(119);r||a(Object.prototype,"toString",o,{unsafe:!0})},function(e,n,t){var r=t(8),a=t(82),o=t(10),i=t(83),s=t(86),c=t(118),u=a("wks"),l=r.Symbol,f=c?l:l&&l.withoutSetter||i;e.exports=function(e){return o(u,e)||(s&&o(l,e)?u[e]=l[e]:u[e]=f("Symbol."+e)),u[e]}},function(e,n){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,n,t){var r=t(8),a=t(40).f,o=t(20),i=t(17),s=t(54),c=t(115),u=t(60);e.exports=function(e,n){var t,l,f,p,d,h=e.target,g=e.global,m=e.stat;if(t=g?r:m?r[h]||s(h,{}):(r[h]||{}).prototype)for(l in n){if(p=n[l],f=e.noTargetGet?(d=a(t,l))&&d.value:t[l],!u(g?l:h+(m?".":"#")+l,e.forced)&&void 0!==f){if(typeof p==typeof f)continue;c(p,f)}(e.sham||f&&f.sham)&&o(p,"sham",!0),i(t,l,p,e)}}},function(e,n,t){(function(n){var t=function(e){return e&&e.Math==Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof n&&n)||Function("return this")()}).call(this,t(113))},function(e,n,t){var r=t(12);e.exports=function(e){if(!r(e))throw TypeError(String(e)+" is not an object");return e}},function(e,n){var t={}.hasOwnProperty;e.exports=function(e,n){return t.call(e,n)}},function(e,n,t){var r=t(6);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(e,n){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,n,t){var r=t(36),a=Math.min;e.exports=function(e){return e>0?a(r(e),9007199254740991):0}},function(e,n,t){"use strict";t.d(n,"a",(function(){return i}));t(22),t(73),t(4),t(2),t(37),t(75),t(31),t(126),t(23),t(128),t(129);var r=t(1),a=t(0),o=t(25);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=Date.now();if(a.a.isInApp()&&a.a.appVersion()>=7.06){var t=e.data?e.data:{};e.data=t,e.url=e.url.replace(/^\s+|\s+$/g,"");var i={method:e.method,url:e.url,status:"Pending",requestType:"MsRequest",startTime:n,endTime:n,response:""};"GET"===e.method?i.getData=e.data:"POST"===e.method&&(i.postData=e.data);var s=Object(o.c)(i);return new Promise((function(n,t){var a=new CustomEvent("msRequestGet",{detail:{data:e.data}});document.dispatchEvent(a),r.a.exec("request",e,(function(e){var t=new CustomEvent("msRequestResolve",{detail:{data:e}});document.dispatchEvent(t),n(e),i.endTime=Date.now(),i.response=e,i.status=200,Object(o.d)(s.id,i)}),(function(e){var n,r=new CustomEvent("msRequestReject",{detail:{data:e}});document.dispatchEvent(r);var a=new Error;try{a.data=JSON.parse(e)}catch(n){a.data=e}t(a),i.endTime=Date.now(),i.response=a,i.status=(null==a||null===(n=a.data)||void 0===n?void 0:n.code)||"0",Object(o.d)(s.id,i)}))}))}var c=e.url?e.url:"",u=e.method?e.method:"GET",l=e.header?e.header:{},f=e.cookies,p=e.data?e.data:{},d=e.requestDataType?e.requestDataType:"FORM",h={};if(f&&Object.keys(f).forEach((function(e){var n=e+"="+f[e]+";domain=10010.com";document.cookie=n})),"post"==u||"POST"==u){var g="";if("JSON"==d)l["Content-Type"]="application/json; charset=UTF-8",g=JSON.stringify(p);else{l["Content-Type"]="application/x-www-form-urlencoded; charset=UTF-8";var m=new URLSearchParams;for(var v in p)m.append(v,p[v]);g=m.toString()}h={body:g,cache:"no-cache",credentials:"include",headers:l,method:"post",mode:"cors"}}else{if(p){var w=new URLSearchParams;for(var y in p)w.append(y,p[y]);-1===c.search(/\?/)?c+="?"+w.toString():c+="&"+w.toString()}h={cache:"no-cache",credentials:"include",headers:l,method:"get",mode:"cors"}}return new Promise((function(e,n){fetch(c,h).then((function(t){t&&"200"==t.status&&t.ok?t.text().then((function(n){e(n)})).catch((function(e){n(e)})):n(new Error(t.toString()))})).catch((function(e){n(e)}))}))}},function(e,n){e.exports=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e}},function(e,n,t){var r=t(11),a=t(78),o=t(9),i=t(42),s=Object.defineProperty;n.f=r?s:function(e,n,t){if(o(e),n=i(n,!0),o(t),a)try{return s(e,n,t)}catch(e){}if("get"in t||"set"in t)throw TypeError("Accessors not supported");return"value"in t&&(e[n]=t.value),e}},function(e,n,t){var r=t(8),a=t(20),o=t(10),i=t(54),s=t(80),c=t(30),u=c.get,l=c.enforce,f=String(String).split("String");(e.exports=function(e,n,t,s){var c=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,p=!!s&&!!s.noTargetGet;"function"==typeof t&&("string"!=typeof n||o(t,"name")||a(t,"name",n),l(t).source=f.join("string"==typeof n?n:"")),e!==r?(c?!p&&e[n]&&(u=!0):delete e[n],u?e[n]=t:a(e,n,t)):u?e[n]=t:i(n,t)})(Function.prototype,"toString",(function(){return"function"==typeof this&&u(this).source||s(this)}))},function(e,n,t){"use strict";function r(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}t.d(n,"a",(function(){return r}))},function(e,n,t){"use strict";function r(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,n,t){return n&&r(e.prototype,n),t&&r(e,t),e}t.d(n,"a",(function(){return a}))},function(e,n,t){var r=t(11),a=t(16),o=t(33);e.exports=r?function(e,n,t){return a.f(e,n,o(1,t))}:function(e,n,t){return e[n]=t,e}},function(e,n,t){var r=t(15);e.exports=function(e){return Object(r(e))}},function(e,n,t){"use strict";var r=t(7),a=t(94);r({target:"Array",proto:!0,forced:[].forEach!=a},{forEach:a})},function(e,n,t){var r=t(8),a=t(95),o=t(94),i=t(20);for(var s in a){var c=r[s],u=c&&c.prototype;if(u&&u.forEach!==o)try{i(u,"forEach",o)}catch(e){u.forEach=o}}},function(e,n,t){var r=t(11),a=t(6),o=t(10),i=Object.defineProperty,s={},c=function(e){throw e};e.exports=function(e,n){if(o(s,e))return s[e];n||(n={});var t=[][e],u=!!o(n,"ACCESSORS")&&n.ACCESSORS,l=o(n,0)?n[0]:c,f=o(n,1)?n[1]:void 0;return s[e]=!!t&&!a((function(){if(u&&!r)return!0;var e={length:-1};u?i(e,1,{enumerable:!0,get:c}):e[1]=1,t.call(e,l,f)}))}},function(e,n,t){"use strict";t.d(n,"b",(function(){return l})),t.d(n,"a",(function(){return f})),t.d(n,"c",(function(){return p})),t.d(n,"d",(function(){return d}));t(105),t(138),t(107),t(53),t(108),t(109),t(73),t(2),t(47);var r=t(3),a=t(32),o=t(39),i=function(e){var n;try{var t=JSON.parse(e);n=!("object"!=Object(a.a)(t)||!t)}catch(e){return!1}return n},s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e&&e.constructor===Object)return Object.keys(e).map((function(n){return'<div class="vc-table-row vc-left-border vc-small">\n            <div class="vc-table-col vc-table-col-2">'.concat(n,'</div> \n            <div class="vc-table-col vc-table-col-4 vc-table-col-value vc-max-height-line">').concat(e[n],"</div>\n        </div>")})).join("")},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return'\n    <i class="vc-table-row-icon">\n        <i class="vc-icon">\n            <svg style="height: 1.1em;width: 1.1em;vertical-align: -0.16em;" class="vc-icon-copy" copy-data="'.concat(encodeURI(e),'" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M5.75 1a.75.75 0 00-.75.75v3c0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75v-3a.75.75 0 00-.75-.75h-4.5zm.75 3V2.5h3V4h-3zm-2.874-.467a.75.75 0 00-.752-1.298A1.75 1.75 0 002 3.75v9.5c0 .966.784 1.75 1.75 1.75h8.5A1.75 1.75 0 0014 13.25v-9.5a1.75 1.75 0 00-.874-1.515.75.75 0 10-.752 1.298.25.25 0 01.126.217v9.5a.25.25 0 01-.25.25h-8.5a.25.25 0 01-.25-.25v-9.5a.25.25 0 01.126-.217z"></path></svg> \n            <svg style="height: 1.1em;width: 1.1em;vertical-align: -0.16em; display:none;" class="vc-icon-copy vc-icon-suc" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path></svg>        \n        </i>\n    </i>\n    ')},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return'\n    <div class="vc-group" id="vc_'.concat(e.id,'">\n        <dl class="vc-table-row vc-group-preview">\n            <dd class="vc-table-col vc-table-col-4">').concat(e.url&&e.url.split("/")[e.url.split("/").length-1],'</dd>\n            <dd class="vc-table-col">').concat(e.method,'</dd>\n            <dd class="vc-table-col">').concat(e.status,'</dd>\n            <dd class="vc-table-col">').concat(Number(e.endTime)-Number(e.startTime),'</dd>\n        </dl>\n        <div class="vc-group-detail">\n            <div>\n                <dl class="vc-table-row vc-left-border">\n                    <dt class="vc-table-col vc-table-col-title">General ').concat(c(e.url),"\n                    </dt>\n                </dl>\n                ").concat(s({URL:e.url,Method:e.method,"Request Type":e.requestType,"HTTP Status":"Pending"===e.status?0:e.status}),"\n            </div>\n        ").concat(function(){if("GET"===e.method)return'\n                    <div>\n                        <dl class="vc-table-row vc-left-border">\n                            <dt class="vc-table-col vc-table-col-title">\n                                <span>Query String Parameters</span>\n                                '.concat(c(JSON.stringify(e.getData)),"\n                            </dt>\n                        </dl>\n                        ").concat(s(e.getData),"\n                    </div>")}(),"\n        ").concat(function(){if("POST"===e.method)return'\n                    <div>\n                        <dl class="vc-table-row vc-left-border">\n                            <dt class="vc-table-col vc-table-col-title">Request Payload\n                                '.concat(c(JSON.stringify(e.postData)),"\n                            </dt>\n                        </dl>\n                        ").concat(s(e.postData),"\n                    </div>")}(),'\n       \n        <div>\n            <dl class="vc-table-row vc-left-border">\n                <dt class="vc-table-col vc-table-col-title">Response\n                    ').concat(c("object"===Object(a.a)(null==e?void 0:e.response)?JSON.stringify(e.response):null==e?void 0:e.response),'\n                </dt>\n            </dl>\n            <div class="vc-table-row vc-left-border vc-small">\n                <pre class="vc-table-col vc-max-height vc-min-height">').concat("object"===Object(a.a)(null==e?void 0:e.response)?JSON.stringify(e.response,null,2):i(null==e?void 0:e.response)?JSON.stringify(JSON.parse(e.response),null,2):null==e?void 0:e.response,"</pre>\n            </div>\n        </div>\n    </div>\n    </div>\n")},l=function(e){var n=document.createElement("textarea");n.style.position="fixed",n.style.top=0,n.style.left=0,n.style.width="2em",n.style.height="2em",n.style.padding=0,n.style.border="none",n.style.outline="none",n.style.boxShadow="none",n.style.background="transparent",n.value=e,document.body.appendChild(n),n.select();var t="";try{t=document.execCommand("copy")?"复制成功！":"复制失败！"}catch(e){t="不能使用这种方法复制内容"}return document.body.removeChild(n),t},f=[],p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=new Array(6).fill("").map((function(){return String.fromCharCode(65+Math.ceil(25*Math.random()))+Math.floor(30*Math.random())})).join(""),t=u(Object(r.a)({id:n},e)),a=document.getElementById("__vc_plug_ms_request_content"),i=new Error("未查询到VConsole");return i.id=n,i.costTime=Date.now(),!o.b&&f.push(Object(r.a)(Object(r.a)({},e),{},{id:n,templates:t})),o.b&&(null==a?void 0:a.insertAdjacentHTML)&&(null==a||a.insertAdjacentHTML("beforeEnd",t)),{id:n,costTime:Date.now()}},d=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=document.getElementById("vc_".concat(e));if(f.length&&f.find((function(t){if(t.id=e)return t.templates=u(Object(r.a)({id:e},n)),t.id=e})),!t)return new Error("未查询到对应的数据");t.innerHTML=u(n)}},function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"c",(function(){return showShareMenu})),__webpack_require__.d(__webpack_exports__,"b",(function(){return shareSingleChannel})),__webpack_require__.d(__webpack_exports__,"d",(function(){return updateClientMoreMenu})),__webpack_require__.d(__webpack_exports__,"a",(function(){return openWxMiniProgram})),__webpack_require__.d(__webpack_exports__,"f",(function(){return updateEdopShareMenu})),__webpack_require__.d(__webpack_exports__,"e",(function(){return updateClientMoreMenuFromID}));var core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(22),core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_array_for_each__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_array_index_of__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(27),core_js_modules_es_array_index_of__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_array_index_of__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(53),core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_array_join__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(4),core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(2),core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(90),core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(31),core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(47),core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_es_string_split__WEBPACK_IMPORTED_MODULE_7__),core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(68),core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_8___default=__webpack_require__.n(core_js_modules_es_string_starts_with__WEBPACK_IMPORTED_MODULE_8__),core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(23),core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_9___default=__webpack_require__.n(core_js_modules_web_dom_collections_for_each__WEBPACK_IMPORTED_MODULE_9__),_comment_ms_jsbridge_js__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(1),_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__(0),_http_index_js__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__(14);function openWxMiniProgram(e){if(_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()){if(!(e&&e.miniProgramUserName&&e.miniProgramPath&&e.miniProgramType))return void alert("打开小程序必须配置miniProgramUserName、miniProgramPath、miniProgramType参数信息");var n={type:_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isIOS()?"WXMiniProgramPay":"miniProgramOpen",shareJson:{miniProgramUserName:e.miniProgramUserName,miniProgramPath:e.miniProgramPath,miniProgramType:e.miniProgramType}};_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(n))}else console.warn("端外不支持调用openWxMiniProgram。")}function handleShareResult(resolve,res){var count,reg="/target/gi";if(reg=eval(reg),count=null==res.match(reg)?0:res.match(reg).length,count>1){var arr=res.split(",");arr[2]=arr[2].replace("target","token"),res=arr.join(",")}resolve(JSON.parse(res))}function showShareMenu(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()?new Promise((function(n,t){if(e){if("image"!=e.shareStyle&&!(e.shareStyle&&e.shareTitle&&e.shareContent&&e.shareIconURL&&e.shareURL))return void alert("shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。");var r="wechat,wechatmoments,qq,qzone,sinaweibo,shortmessage,wokouling,huabaofenxiang";if(e.shareChannel&&Array.isArray(e.shareChannel)&&e.shareChannel.length>0){if(1==e.shareChannel.length)return void alert("调用showShareMenu方法，如果配置了shareChannel参数，则至少要配置两个分享渠道");r=e.shareChannel.join(",")}var a={shareTitle:e.shareTitle,shareContent:e.shareContent,shareIconURL:e.shareIconURL,shareURL:e.shareURL,provider:e.provider?e.provider:"10010",businessCode:e.businessCode?e.businessCode:"9991111"};if(e.shareStyle&&"webpage"==e.shareStyle)r=r.replace("wokouling","").replace("huabaofenxiang",""),a.shareType="url";else if(e.shareStyle&&"pictorial"==e.shareStyle){if(!e.huabaoIconUrl)return void alert("当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");a.shareType="url";var o=e.huabaoIconUrl;o.startsWith("http")||(o=o.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.huabaoIconUrl=o}else{if(!e.shareStyle||"image"!=e.shareStyle)return void alert("shareStyle属性只能配置webpage、pictorial、image");if(!e.shareImage)return void alert("当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");r=r.replace("wokouling","").replace("huabaofenxiang","").replace("shortmessage",""),a.shareType="image";var i=e.shareImage;i.startsWith("http")||(i=i.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.templetImg=i}if(e.miniProgramShare&&"1"==e.miniProgramShare){if(!(e.miniProgramType&&e.miniProgramUserName&&e.miniProgramPath))return void alert("当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");a.miniProgramShare=e.miniProgramShare,a.miniProgramType=e.miniProgramType,a.miniProgramUserName=e.miniProgramUserName,a.miniProgramPath=e.miniProgramPath}var s={type:"share2",shareList:r,shareJson:a};window.setShareStatus_Local=function(e){handleShareResult(n,e)},shareEventDistribution(s.shareJson,(function(){_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}))}else alert("分享配置不能为空")})):(console.warn("当前环境不支持调用showShareMenu方法。"),new Promise((function(e,n){})))}function shareSingleChannel(e){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()?new Promise((function(n,t){if("image"==e.shareStyle||e.shareStyle&&e.shareTitle&&e.shareContent&&e.shareIconURL&&e.shareURL)if(e.shareChannel&&Array.isArray(e.shareChannel)&&e.shareChannel.length>0){var r=e.shareChannel[0],a={shareTitle:e.shareTitle,shareContent:e.shareContent,shareIconURL:e.shareIconURL,shareURL:e.shareURL,provider:e.provider?e.provider:"10010",businessCode:e.businessCode?e.businessCode:"9991111"};if(e.shareStyle&&"webpage"==e.shareStyle){if("wokouling"==r||"huabaofenxiang"==r)return void alert("当shareStyle配置为webpage时，shareChannel不能配置wokouling、huabaofenxiang，请检查配置项。");a.shareType="url"}else if(e.shareStyle&&"pictorial"==e.shareStyle){if(!e.huabaoIconUrl)return void alert("当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");a.shareType="url";var o=e.huabaoIconUrl;o.startsWith("http")||(o=o.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.huabaoIconUrl=o}else{if(!e.shareStyle||"image"!=e.shareStyle)return void alert("shareStyle属性只能配置webpage、pictorial、image");if(!e.shareImage)return void alert("当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");if("wokouling"==r||"huabaofenxiang"==r||"shortmessage"==r)return void alert("当shareStyle配置为image时，shareChannel不能配置wokouling、huabaofenxiang、shortmessage，请检查配置项。");a.shareType="image";var i=e.shareImage;i.startsWith("http")||(i=i.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),a.templetImg=i}if(e.miniProgramShare&&"1"==e.miniProgramShare){if(!(e.miniProgramType&&e.miniProgramUserName&&e.miniProgramPath))return void alert("当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");a.miniProgramShare=e.miniProgramShare,a.miniProgramType=e.miniProgramType,a.miniProgramUserName=e.miniProgramUserName,a.miniProgramPath=e.miniProgramPath}var s={type:"shareDirectly",shareList:r,shareJson:a};window.setShareStatus_Local=function(e){handleShareResult(n,e)},shareEventDistribution(s.shareJson,(function(){_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isIOS()?_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}))}else alert("shareChannel类型必须为数组且不能为空");else alert("shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。")})):(console.warn("当前环境不支持调用shareSingleChannel方法。"),new Promise((function(e,n){})))}function updateClientMoreMenu(menuParams,onShareSuccess){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()?new Promise((function(resolve,reject){if(menuParams){var shareList=null,shareJson=null,longScreenshotConfig=null;if(menuParams.shareConfig){var params=menuParams.shareConfig;if("image"!=params.shareStyle&&!(params.shareStyle&&params.shareTitle&&params.shareContent&&params.shareIconURL&&params.shareURL))return void alert("调用updateClientMoreMenu方法错误：shareStyle、shareTitle、shareContent、shareIconURL、shareURL 参数必须配置，请检查配置项。");if(shareList="wechat,wechatmoments,qq,qzone,sinaweibo,shortmessage,wokouling,huabaofenxiang",params.shareChannel&&Array.isArray(params.shareChannel)&&params.shareChannel.length>0){if(1==params.shareChannel.length)return void alert("调用updateClientMoreMenu方法错误：如果配置了shareChannel参数，则至少要配置两个分享渠道");shareList=params.shareChannel.join(",")}if(shareJson={shareTitle:params.shareTitle,shareContent:params.shareContent,shareIconURL:params.shareIconURL,shareURL:params.shareURL,provider:params.provider?params.provider:"10010",businessCode:params.businessCode?params.businessCode:"9991111"},params.shareStyle&&"webpage"==params.shareStyle)shareList=shareList.replace("wokouling","").replace("huabaofenxiang",""),shareJson.shareType="url";else if(params.shareStyle&&"pictorial"==params.shareStyle){if(!params.huabaoIconUrl)return void alert("调用updateClientMoreMenu方法错误：当shareStyle配置为pictorial时，huabaoIconUrl参数必须配置，请检查配置项。");shareJson.shareType="url";var huabaoIconUrl=params.huabaoIconUrl;huabaoIconUrl.startsWith("http")||(huabaoIconUrl=huabaoIconUrl.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),shareJson.huabaoIconUrl=huabaoIconUrl}else{if(!params.shareStyle||"image"!=params.shareStyle)return void alert("调用updateClientMoreMenu方法错误：shareStyle属性只能配置webpage、pictorial、image");if(!params.shareImage)return void alert("调用updateClientMoreMenu方法错误：当shareStyle配置为image时，shareImage参数必须配置，请检查配置项。");shareList=shareList.replace("wokouling","").replace("huabaofenxiang","").replace("shortmessage",""),shareJson.shareType="image";var shareImage=params.shareImage;shareImage.startsWith("http")||(shareImage=shareImage.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,","")),shareJson.templetImg=shareImage}if(params.miniProgramShare&&"1"==params.miniProgramShare){if(!(params.miniProgramType&&params.miniProgramUserName&&params.miniProgramPath))return void alert("调用updateClientMoreMenu方法错误：当miniProgramShare配置为1时,miniProgramType、miniProgramUserName、miniProgramPath 参数必须配置，请检查配置项。");shareJson.miniProgramShare=params.miniProgramShare,shareJson.miniProgramType=params.miniProgramType,shareJson.miniProgramUserName=params.miniProgramUserName,shareJson.miniProgramPath=params.miniProgramPath}longScreenshotConfig={shareType:"longScreenshot",shareTitle:shareJson.shareTitle,shareContent:shareJson.shareContent,shareURL:shareJson.shareURL,shareIconURL:shareJson.shareIconURL,shareQrcodeURL:params.shareQrcodeURL,provider:params.provider?params.provider:"",businessCode:params.businessCode?params.businessCode:"",businessName:params.businessName?params.businessName:""}}var moreMenuConfig=[],moreMenu=["share","screenshotShare","feedback","goHome","shuaxin"];menuParams.moreMenu&&Array.isArray(menuParams.moreMenu)&&menuParams.moreMenu.length>0&&(moreMenu=menuParams.moreMenu),moreMenu.forEach((function(e){"share"==e?moreMenuConfig.push({code:"fenxiang",title:"分享",shareList:shareList,shareJson:shareJson}):"screenshotShare"==e?moreMenuConfig.push({code:"jietufenxiang",title:"截图分享",shareList:shareList,shareJson:longScreenshotConfig}):"feedback"==e?moreMenuConfig.push({code:"tucao",title:"反馈与建议",desc:"反馈与建议"}):"goHome"==e?moreMenuConfig.push({code:"shouye",title:"首页",desc:"回到首页"}):"shuaxin"==e&&moreMenuConfig.push({code:"shuaxin",title:"刷新",desc:"刷新页面"})}));var menuConfig={config:moreMenuConfig};window.getMenuConfig_Local=function(){return JSON.stringify(menuConfig)}}window.setShareStatus_Local=function(res){var count,reg="/target/gi";if(reg=eval(reg),count=null==res.match(reg)?0:res.match(reg).length,count>1){var arr=res.split(",");arr[2]=arr[2].replace("target","token"),res=arr.join(",")}var resultRes=JSON.parse(res);menuParams.onShareSuccess&&menuParams.onShareSuccess instanceof Function?menuParams.onShareSuccess(resultRes):onShareSuccess&&onShareSuccess instanceof Function&&onShareSuccess(resultRes)}})):(console.warn("当前环境不支持调用updateClientMoreMenu方法。"),new Promise((function(e,n){})))}function updateEdopShareMenu(e,n){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()&&_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.appVersion()>=9?isEdopShare(e)?new Promise((function(t,r){window.setShareStatus_updateEdopShareMenu=function(e){n&&n instanceof Function&&n(e)},_comment_ms_jsbridge_js__WEBPACK_IMPORTED_MODULE_10__.a.exec("updateEdopShareMenu",{shareConfig:e})})):(console.warn("小程序分享需带有分享中间页"),new Promise((function(e,n){}))):(console.warn("当前环境不支持调用updateEdopShareMenu方法。"),new Promise((function(e,n){})))}function updateClientMoreMenuFromID(e,n){return _comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isInApp()?new Promise((function(t,r){if(e&&e.shareId){var a={url:"https://m.client.10010.com/mobileService/customer/getshareredisinfoh5.htm",data:e,timeout:3e3};Object(_http_index_js__WEBPACK_IMPORTED_MODULE_12__.a)(a).then((function(r){updateClientMoreMenu(JSON.parse(r),(function(t){e.onShareSuccess&&e.onShareSuccess instanceof Function?e.onShareSuccess(t):n&&n instanceof Function&&n(t)})),t(r)})).catch((function(e){var n=new Error;n.data=e.data,r(n)}))}else{var o=new Error;o.data="调用updateClientMoreMenuFromID方法缺少关键参数",r(o)}})):(console.warn("端外不支持调用updateClientMoreMenuFromID方法。"),new Promise((function(e,n){var t=new Error;t.data="端外不支持调用updateClientMoreMenuFromID方法。",n(t)})))}function shareEventDistribution(e,n){_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isEdop()&&e.shareURL?e.shareURL.indexOf("https://img.client.10010.com/dwguide/index.html")>-1||e.shareURL.indexOf("https://wap.10010.com/t/clickCountLogRecord/pageClickCount.htm")>-1||e.shareURL.indexOf("https://u.10010.cn/")>-1||e.shareURL.indexOf("https://m.10010.cn/")>-1?n&&"function"==typeof n&&n():console.warn("小程序分享需带有分享中间页"):n&&"function"==typeof n&&n()}function isEdopShare(e){return!_comment_ms_util__WEBPACK_IMPORTED_MODULE_11__.a.isEdop()||!e.shareURL||(e.shareURL.indexOf("https://img.client.10010.com/dwguide/index.html")>-1||e.shareURL.indexOf("https://wap.10010.com/t/clickCountLogRecord/pageClickCount.htm")>-1||e.shareURL.indexOf("https://u.10010.cn/")>-1||e.shareURL.indexOf("https://m.10010.cn/")>-1)}},function(e,n,t){"use strict";var r=t(7),a=t(58).indexOf,o=t(61),i=t(24),s=[].indexOf,c=!!s&&1/[1].indexOf(1,-0)<0,u=o("indexOf"),l=i("indexOf",{ACCESSORS:!0,1:0});r({target:"Array",proto:!0,forced:c||!u||!l},{indexOf:function(e){return c?s.apply(this,arguments)||0:a(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,n,t){var r=t(41),a=t(15);e.exports=function(e){return r(a(e))}},function(e,n){var t={}.toString;e.exports=function(e){return t.call(e).slice(8,-1)}},function(e,n,t){var r,a,o,i=t(114),s=t(8),c=t(12),u=t(20),l=t(10),f=t(55),p=t(56),d=s.WeakMap;if(i){var h=new d,g=h.get,m=h.has,v=h.set;r=function(e,n){return v.call(h,e,n),n},a=function(e){return g.call(h,e)||{}},o=function(e){return m.call(h,e)}}else{var w=f("state");p[w]=!0,r=function(e,n){return u(e,w,n),n},a=function(e){return l(e,w)?e[w]:{}},o=function(e){return l(e,w)}}e.exports={set:r,get:a,has:o,enforce:function(e){return o(e)?a(e):r(e,{})},getterFor:function(e){return function(n){var t;if(!c(n)||(t=a(n)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return t}}}},function(e,n,t){"use strict";var r=t(45),a=t(9),o=t(21),i=t(13),s=t(36),c=t(15),u=t(66),l=t(46),f=Math.max,p=Math.min,d=Math.floor,h=/\$([$&'`]|\d\d?|<[^>]*>)/g,g=/\$([$&'`]|\d\d?)/g;r("replace",2,(function(e,n,t,r){var m=r.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,v=r.REPLACE_KEEPS_$0,w=m?"$":"$0";return[function(t,r){var a=c(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,a,r):n.call(String(a),t,r)},function(e,r){if(!m&&v||"string"==typeof r&&-1===r.indexOf(w)){var o=t(n,e,this,r);if(o.done)return o.value}var c=a(e),d=String(this),h="function"==typeof r;h||(r=String(r));var g=c.global;if(g){var _=c.unicode;c.lastIndex=0}for(var S=[];;){var E=l(c,d);if(null===E)break;if(S.push(E),!g)break;""===String(E[0])&&(c.lastIndex=u(d,i(c.lastIndex),_))}for(var b,P="",A=0,I=0;I<S.length;I++){E=S[I];for(var x=String(E[0]),C=f(p(s(E.index),d.length),0),O=[],k=1;k<E.length;k++)O.push(void 0===(b=E[k])?b:String(b));var M=E.groups;if(h){var T=[x].concat(O,C,d);void 0!==M&&T.push(M);var L=String(r.apply(void 0,T))}else L=y(x,d,C,O,M,r);C>=A&&(P+=d.slice(A,C)+L,A=C+x.length)}return P+d.slice(A)}];function y(e,t,r,a,i,s){var c=r+e.length,u=a.length,l=g;return void 0!==i&&(i=o(i),l=h),n.call(s,l,(function(n,o){var s;switch(o.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(c);case"<":s=i[o.slice(1,-1)];break;default:var l=+o;if(0===l)return n;if(l>u){var f=d(l/10);return 0===f?n:f<=u?void 0===a[f-1]?o.charAt(1):a[f-1]+o.charAt(1):n}s=a[l-1]}return void 0===s?"":s}))}}))},function(e,n,t){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.d(n,"a",(function(){return r}))},function(e,n){e.exports=function(e,n){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:n}}},function(e,n){e.exports=!1},function(e,n,t){var r=t(117),a=t(8),o=function(e){return"function"==typeof e?e:void 0};e.exports=function(e,n){return arguments.length<2?o(r[e])||o(a[e]):r[e]&&r[e][n]||a[e]&&a[e][n]}},function(e,n){var t=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:t)(e)}},function(e,n,t){"use strict";var r=t(17),a=t(9),o=t(6),i=t(65),s=RegExp.prototype,c=s.toString,u=o((function(){return"/a/b"!=c.call({source:"a",flags:"b"})})),l="toString"!=c.name;(u||l)&&r(RegExp.prototype,"toString",(function(){var e=a(this),n=String(e.source),t=e.flags;return"/"+n+"/"+String(void 0===t&&e instanceof RegExp&&!("flags"in s)?i.call(e):t)}),{unsafe:!0})},function(e,n){e.exports={}},function(e,n,t){"use strict";t.d(n,"b",(function(){return c}));t(107),t(142),t(110),t(53),t(108),t(4),t(111),t(75);var r=t(3),a=t(32),o=t(18),i=t(19),s=t(25),c=!1,u=function(){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object(o.a)(this,e),this.resolve=n.resolve,this.reject=n.reject,this.LivingExample=n.vconsole,this.create(n.options)}return Object(i.a)(e,[{key:"create",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.LivingExample&&this.LivingExample.constructor===Function)try{this.vConsole=new this.LivingExample(e),this.createPlugin(),this.resolve(this.vConsole)}catch(e){this.reject("实例vConsole失败",e),console.warn("实例vConsole失败",e)}else this.reject("实例vConsole失败"),console.warn("实例vConsole失败")}},{key:"createPlugin",value:function(){var e,n,t=this,r=(null===(e=this.LivingExample)||void 0===e?void 0:e.VConsolePlugin)&&new this.LivingExample.VConsolePlugin("ms_request","MsRequest");if(r){r.on("renderTab",(function(e){e('\n            <div class="vc-table">\n                <dl class="vc-table-row">\n                    <dd class="vc-table-col vc-table-col-4">Name</dd>\n                    <dd class="vc-table-col">Method</dd>\n                    <dd class="vc-table-col">Status</dd>\n                    <dd class="vc-table-col">Time</dd>\n                </dl>\n                <div class="vc-plugin-content" id="__vc_plug_ms_request_content">\n                    \n                </div>\n            </div>\n            ')}),!0);var a=0;r.on("ready",(function e(){++a>5?t.reject("实例vConsole失败，注入扩展超时",error):document.getElementById("__vc_plug_ms_request_content")?(document.getElementById("__vc_plug_ms_request_content").innerHTML="".concat(s.a.map((function(e){return e.templates})).join("")),c=!0,t.delegate(document.getElementById("__vc_plug_ms_request_content"),"click","vc-group-preview",(function(){this.parentElement.className.includes("vc-actived")?this.parentElement.classList.remove("vc-actived"):this.parentElement.classList.add("vc-actived")})),t.delegate(document.getElementById("__vc_plug_ms_request_content"),"click","vc-icon-copy",(function(){var e=this;Object(s.b)(decodeURI(this.getAttribute("copy-data"))||" "),this.style.display="none",this.parentElement.getElementsByClassName("vc-icon-suc")[0].style.display="initial",setTimeout((function(){e.style.display="initial",e.parentElement.getElementsByClassName("vc-icon-suc")[0].style.display="none"}),3e3)}))):setTimeout(e,300)})),null===(n=this.vConsole)||void 0===n||n.addPlugin(r)}else console.warn("不支持vConsole-MsRequest扩展插件")}},{key:"delegate",value:function(e,n,t,r){return e.addEventListener(n,(function(n){for(var o=n.target;"object"===Object(a.a)(null===(i=o)||void 0===i?void 0:i.className)?!Array.from(null===(s=o)||void 0===s?void 0:s.classList).find((function(e){return e===t})):null===(c=o)||void 0===c||null===(u=c.className)||void 0===u||!u.includes(t);){var i,s,c,u;if(e===o){o=null;break}o=o.parentNode}o&&r.call(o,n,o)})),e}}]),e}();n.a=function(e){return new Promise((function(n,t){new u(Object(r.a)({resolve:n,reject:t},e))}))}},function(e,n,t){var r=t(11),a=t(77),o=t(33),i=t(28),s=t(42),c=t(10),u=t(78),l=Object.getOwnPropertyDescriptor;n.f=r?l:function(e,n){if(e=i(e),n=s(n,!0),u)try{return l(e,n)}catch(e){}if(c(e,n))return o(!a.f.call(e,n),e[n])}},function(e,n,t){var r=t(6),a=t(29),o="".split;e.exports=r((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==a(e)?o.call(e,""):Object(e)}:Object},function(e,n,t){var r=t(12);e.exports=function(e,n){if(!r(e))return e;var t,a;if(n&&"function"==typeof(t=e.toString)&&!r(a=t.call(e)))return a;if("function"==typeof(t=e.valueOf)&&!r(a=t.call(e)))return a;if(!n&&"function"==typeof(t=e.toString)&&!r(a=t.call(e)))return a;throw TypeError("Can't convert object to primitive value")}},function(e,n,t){var r=t(36),a=Math.max,o=Math.min;e.exports=function(e,n){var t=r(e);return t<0?a(t+n,0):o(t,n)}},function(e,n,t){"use strict";var r,a,o=t(65),i=t(89),s=RegExp.prototype.exec,c=String.prototype.replace,u=s,l=(r=/a/,a=/b*/g,s.call(r,"a"),s.call(a,"a"),0!==r.lastIndex||0!==a.lastIndex),f=i.UNSUPPORTED_Y||i.BROKEN_CARET,p=void 0!==/()??/.exec("")[1];(l||p||f)&&(u=function(e){var n,t,r,a,i=this,u=f&&i.sticky,d=o.call(i),h=i.source,g=0,m=e;return u&&(-1===(d=d.replace("y","")).indexOf("g")&&(d+="g"),m=String(e).slice(i.lastIndex),i.lastIndex>0&&(!i.multiline||i.multiline&&"\n"!==e[i.lastIndex-1])&&(h="(?: "+h+")",m=" "+m,g++),t=new RegExp("^(?:"+h+")",d)),p&&(t=new RegExp("^"+h+"$(?!\\s)",d)),l&&(n=i.lastIndex),r=s.call(u?t:i,m),u?r?(r.input=r.input.slice(g),r[0]=r[0].slice(g),r.index=i.lastIndex,i.lastIndex+=r[0].length):i.lastIndex=0:l&&r&&(i.lastIndex=i.global?r.index+r[0].length:n),p&&r&&r.length>1&&c.call(r[0],t,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(r[a]=void 0)})),r}),e.exports=u},function(e,n,t){"use strict";t(2);var r=t(17),a=t(6),o=t(5),i=t(44),s=t(20),c=o("species"),u=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),l="$0"==="a".replace(/./,"$0"),f=o("replace"),p=!!/./[f]&&""===/./[f]("a","$0"),d=!a((function(){var e=/(?:)/,n=e.exec;e.exec=function(){return n.apply(this,arguments)};var t="ab".split(e);return 2!==t.length||"a"!==t[0]||"b"!==t[1]}));e.exports=function(e,n,t,f){var h=o(e),g=!a((function(){var n={};return n[h]=function(){return 7},7!=""[e](n)})),m=g&&!a((function(){var n=!1,t=/a/;return"split"===e&&((t={}).constructor={},t.constructor[c]=function(){return t},t.flags="",t[h]=/./[h]),t.exec=function(){return n=!0,null},t[h](""),!n}));if(!g||!m||"replace"===e&&(!u||!l||p)||"split"===e&&!d){var v=/./[h],w=t(h,""[e],(function(e,n,t,r,a){return n.exec===i?g&&!a?{done:!0,value:v.call(n,t,r)}:{done:!0,value:e.call(t,n,r)}:{done:!1}}),{REPLACE_KEEPS_$0:l,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:p}),y=w[0],_=w[1];r(String.prototype,e,y),r(RegExp.prototype,h,2==n?function(e,n){return _.call(e,this,n)}:function(e){return _.call(e,this)})}f&&s(RegExp.prototype[h],"sham",!0)}},function(e,n,t){var r=t(29),a=t(44);e.exports=function(e,n){var t=e.exec;if("function"==typeof t){var o=t.call(e,n);if("object"!=typeof o)throw TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==r(e))throw TypeError("RegExp#exec called on incompatible receiver");return a.call(e,n)}},function(e,n,t){"use strict";var r=t(45),a=t(64),o=t(9),i=t(15),s=t(123),c=t(66),u=t(13),l=t(46),f=t(44),p=t(6),d=[].push,h=Math.min,g=!p((function(){return!RegExp(4294967295,"y")}));r("split",2,(function(e,n,t){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,t){var r=String(i(this)),o=void 0===t?4294967295:t>>>0;if(0===o)return[];if(void 0===e)return[r];if(!a(e))return n.call(r,e,o);for(var s,c,u,l=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),h=0,g=new RegExp(e.source,p+"g");(s=f.call(g,r))&&!((c=g.lastIndex)>h&&(l.push(r.slice(h,s.index)),s.length>1&&s.index<r.length&&d.apply(l,s.slice(1)),u=s[0].length,h=c,l.length>=o));)g.lastIndex===s.index&&g.lastIndex++;return h===r.length?!u&&g.test("")||l.push(""):l.push(r.slice(h)),l.length>o?l.slice(0,o):l}:"0".split(void 0,0).length?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,t){var a=i(this),o=null==n?void 0:n[e];return void 0!==o?o.call(n,a,t):r.call(String(a),n,t)},function(e,a){var i=t(r,e,this,a,r!==n);if(i.done)return i.value;var f=o(e),p=String(this),d=s(f,RegExp),m=f.unicode,v=(f.ignoreCase?"i":"")+(f.multiline?"m":"")+(f.unicode?"u":"")+(g?"y":"g"),w=new d(g?f:"^(?:"+f.source+")",v),y=void 0===a?4294967295:a>>>0;if(0===y)return[];if(0===p.length)return null===l(w,p)?[p]:[];for(var _=0,S=0,E=[];S<p.length;){w.lastIndex=g?S:0;var b,P=l(w,g?p:p.slice(S));if(null===P||(b=h(u(w.lastIndex+(g?0:S)),p.length))===_)S=c(p,S,m);else{if(E.push(p.slice(_,S)),E.length===y)return E;for(var A=1;A<=P.length-1;A++)if(E.push(P[A]),E.length===y)return E;S=_=b}}return E.push(p.slice(_)),E}]}),!g)},function(e,n,t){var r,a=t(9),o=t(100),i=t(59),s=t(56),c=t(125),u=t(79),l=t(55),f=l("IE_PROTO"),p=function(){},d=function(e){return"<script>"+e+"<\/script>"},h=function(){try{r=document.domain&&new ActiveXObject("htmlfile")}catch(e){}var e,n;h=r?function(e){e.write(d("")),e.close();var n=e.parentWindow.Object;return e=null,n}(r):((n=u("iframe")).style.display="none",c.appendChild(n),n.src=String("javascript:"),(e=n.contentWindow.document).open(),e.write(d("document.F=Object")),e.close(),e.F);for(var t=i.length;t--;)delete h.prototype[i[t]];return h()};s[f]=!0,e.exports=Object.create||function(e,n){var t;return null!==e?(p.prototype=a(e),t=new p,p.prototype=null,t[f]=e):t=h(),void 0===n?t:o(t,n)}},function(e,n,t){var r=t(16).f,a=t(10),o=t(5)("toStringTag");e.exports=function(e,n,t){e&&!a(e=t?e:e.prototype,o)&&r(e,o,{configurable:!0,value:n})}},function(e,n,t){var r=t(5),a=t(48),o=t(16),i=r("unscopables"),s=Array.prototype;null==s[i]&&o.f(s,i,{configurable:!0,value:a(null)}),e.exports=function(e){s[i][e]=!0}},function(e,n,t){"use strict";var r=t(42),a=t(16),o=t(33);e.exports=function(e,n,t){var i=r(n);i in e?a.f(e,i,o(0,t)):e[i]=t}},function(e,n,t){var r=t(6),a=t(5),o=t(106),i=a("species");e.exports=function(e){return o>=51||!r((function(){var n=[];return(n.constructor={})[i]=function(){return{foo:1}},1!==n[e](Boolean).foo}))}},function(e,n,t){"use strict";var r=t(7),a=t(41),o=t(28),i=t(61),s=[].join,c=a!=Object,u=i("join",",");r({target:"Array",proto:!0,forced:c||!u},{join:function(e){return s.call(o(this),void 0===e?",":e)}})},function(e,n,t){var r=t(8),a=t(20);e.exports=function(e,n){try{a(r,e,n)}catch(t){r[e]=n}return n}},function(e,n,t){var r=t(82),a=t(83),o=r("keys");e.exports=function(e){return o[e]||(o[e]=a(e))}},function(e,n){e.exports={}},function(e,n,t){var r=t(84),a=t(59).concat("length","prototype");n.f=Object.getOwnPropertyNames||function(e){return r(e,a)}},function(e,n,t){var r=t(28),a=t(13),o=t(43),i=function(e){return function(n,t,i){var s,c=r(n),u=a(c.length),l=o(i,u);if(e&&t!=t){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===t)return e||l||0;return!e&&-1}};e.exports={includes:i(!0),indexOf:i(!1)}},function(e,n){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(e,n,t){var r=t(6),a=/#|\.prototype\./,o=function(e,n){var t=s[i(e)];return t==u||t!=c&&("function"==typeof n?r(n):!!n)},i=o.normalize=function(e){return String(e).replace(a,".").toLowerCase()},s=o.data={},c=o.NATIVE="N",u=o.POLYFILL="P";e.exports=o},function(e,n,t){"use strict";var r=t(6);e.exports=function(e,n){var t=[][e];return!!t&&r((function(){t.call(null,n||function(){throw 1},1)}))}},function(e,n,t){var r={};r[t(5)("toStringTag")]="z",e.exports="[object z]"===String(r)},function(e,n,t){var r=t(62),a=t(29),o=t(5)("toStringTag"),i="Arguments"==a(function(){return arguments}());e.exports=r?a:function(e){var n,t,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=function(e,n){try{return e[n]}catch(e){}}(n=Object(e),o))?t:i?a(n):"Object"==(r=a(n))&&"function"==typeof n.callee?"Arguments":r}},function(e,n,t){var r=t(12),a=t(29),o=t(5)("match");e.exports=function(e){var n;return r(e)&&(void 0!==(n=e[o])?!!n:"RegExp"==a(e))}},function(e,n,t){"use strict";var r=t(9);e.exports=function(){var e=r(this),n="";return e.global&&(n+="g"),e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.dotAll&&(n+="s"),e.unicode&&(n+="u"),e.sticky&&(n+="y"),n}},function(e,n,t){"use strict";var r=t(67).charAt;e.exports=function(e,n,t){return n+(t?r(e,n).length:1)}},function(e,n,t){var r=t(36),a=t(15),o=function(e){return function(n,t){var o,i,s=String(a(n)),c=r(t),u=s.length;return c<0||c>=u?e?"":void 0:(o=s.charCodeAt(c))<55296||o>56319||c+1===u||(i=s.charCodeAt(c+1))<56320||i>57343?e?s.charAt(c):o:e?s.slice(c,c+2):i-56320+(o-55296<<10)+65536}};e.exports={codeAt:o(!1),charAt:o(!0)}},function(e,n,t){"use strict";var r,a=t(7),o=t(40).f,i=t(13),s=t(92),c=t(15),u=t(93),l=t(34),f="".startsWith,p=Math.min,d=u("startsWith");a({target:"String",proto:!0,forced:!!(l||d||(r=o(String.prototype,"startsWith"),!r||r.writable))&&!d},{startsWith:function(e){var n=String(c(this));s(e);var t=i(p(arguments.length>1?arguments[1]:void 0,n.length)),r=String(e);return f?f.call(n,r,t):n.slice(t,t+r.length)===r}})},function(e,n,t){var r=t(70),a=t(41),o=t(21),i=t(13),s=t(71),c=[].push,u=function(e){var n=1==e,t=2==e,u=3==e,l=4==e,f=6==e,p=5==e||f;return function(d,h,g,m){for(var v,w,y=o(d),_=a(y),S=r(h,g,3),E=i(_.length),b=0,P=m||s,A=n?P(d,E):t?P(d,0):void 0;E>b;b++)if((p||b in _)&&(w=S(v=_[b],b,y),e))if(n)A[b]=w;else if(w)switch(e){case 3:return!0;case 5:return v;case 6:return b;case 2:c.call(A,v)}else if(l)return!1;return f?-1:u||l?l:A}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6)}},function(e,n,t){var r=t(91);e.exports=function(e,n,t){if(r(e),void 0===n)return e;switch(t){case 0:return function(){return e.call(n)};case 1:return function(t){return e.call(n,t)};case 2:return function(t,r){return e.call(n,t,r)};case 3:return function(t,r,a){return e.call(n,t,r,a)}}return function(){return e.apply(n,arguments)}}},function(e,n,t){var r=t(12),a=t(72),o=t(5)("species");e.exports=function(e,n){var t;return a(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!a(t.prototype)?r(t)&&null===(t=t[o])&&(t=void 0):t=void 0),new(void 0===t?Array:t)(0===n?0:n)}},function(e,n,t){var r=t(29);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,n,t){var r=t(7),a=t(21),o=t(74);r({target:"Object",stat:!0,forced:t(6)((function(){o(1)}))},{keys:function(e){return o(a(e))}})},function(e,n,t){var r=t(84),a=t(59);e.exports=Object.keys||function(e){return r(e,a)}},function(e,n,t){"use strict";var r=t(67).charAt,a=t(30),o=t(96),i=a.set,s=a.getterFor("String Iterator");o(String,"String",(function(e){i(this,{type:"String Iterator",string:String(e),index:0})}),(function(){var e,n=s(this),t=n.string,a=n.index;return a>=t.length?{value:void 0,done:!0}:(e=r(t,a),n.index+=e.length,{value:e,done:!1})}))},function(e,n,t){var r=t(63),a=t(38),o=t(5)("iterator");e.exports=function(e){if(null!=e)return e[o]||e["@@iterator"]||a[r(e)]}},function(e,n,t){"use strict";var r={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,o=a&&!r.call({1:2},1);n.f=o?function(e){var n=a(this,e);return!!n&&n.enumerable}:r},function(e,n,t){var r=t(11),a=t(6),o=t(79);e.exports=!r&&!a((function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},function(e,n,t){var r=t(8),a=t(12),o=r.document,i=a(o)&&a(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},function(e,n,t){var r=t(81),a=Function.toString;"function"!=typeof r.inspectSource&&(r.inspectSource=function(e){return a.call(e)}),e.exports=r.inspectSource},function(e,n,t){var r=t(8),a=t(54),o=r["__core-js_shared__"]||a("__core-js_shared__",{});e.exports=o},function(e,n,t){var r=t(34),a=t(81);(e.exports=function(e,n){return a[e]||(a[e]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.5",mode:r?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,n){var t=0,r=Math.random();e.exports=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++t+r).toString(36)}},function(e,n,t){var r=t(10),a=t(28),o=t(58).indexOf,i=t(56);e.exports=function(e,n){var t,s=a(e),c=0,u=[];for(t in s)!r(i,t)&&r(s,t)&&u.push(t);for(;n.length>c;)r(s,t=n[c++])&&(~o(u,t)||u.push(t));return u}},function(e,n){n.f=Object.getOwnPropertySymbols},function(e,n,t){var r=t(6);e.exports=!!Object.getOwnPropertySymbols&&!r((function(){return!String(Symbol())}))},function(e,n,t){var r=t(12),a=t(88);e.exports=function(e,n,t){var o,i;return a&&"function"==typeof(o=n.constructor)&&o!==t&&r(i=o.prototype)&&i!==t.prototype&&a(e,i),e}},function(e,n,t){var r=t(9),a=t(121);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,n=!1,t={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(t,[]),n=t instanceof Array}catch(e){}return function(t,o){return r(t),a(o),n?e.call(t,o):t.__proto__=o,t}}():void 0)},function(e,n,t){"use strict";var r=t(6);function a(e,n){return RegExp(e,n)}n.UNSUPPORTED_Y=r((function(){var e=a("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),n.BROKEN_CARET=r((function(){var e=a("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},function(e,n,t){"use strict";var r=t(45),a=t(9),o=t(13),i=t(15),s=t(66),c=t(46);r("match",1,(function(e,n,t){return[function(n){var t=i(this),r=null==n?void 0:n[e];return void 0!==r?r.call(n,t):new RegExp(n)[e](String(t))},function(e){var r=t(n,e,this);if(r.done)return r.value;var i=a(e),u=String(this);if(!i.global)return c(i,u);var l=i.unicode;i.lastIndex=0;for(var f,p=[],d=0;null!==(f=c(i,u));){var h=String(f[0]);p[d]=h,""===h&&(i.lastIndex=s(u,o(i.lastIndex),l)),d++}return 0===d?null:p}]}))},function(e,n){e.exports=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e}},function(e,n,t){var r=t(64);e.exports=function(e){if(r(e))throw TypeError("The method doesn't accept regular expressions");return e}},function(e,n,t){var r=t(5)("match");e.exports=function(e){var n=/./;try{"/./"[e](n)}catch(t){try{return n[r]=!1,"/./"[e](n)}catch(e){}}return!1}},function(e,n,t){"use strict";var r=t(69).forEach,a=t(61),o=t(24),i=a("forEach"),s=o("forEach");e.exports=i&&s?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},function(e,n){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(e,n,t){"use strict";var r=t(7),a=t(97),o=t(99),i=t(88),s=t(49),c=t(20),u=t(17),l=t(5),f=t(34),p=t(38),d=t(98),h=d.IteratorPrototype,g=d.BUGGY_SAFARI_ITERATORS,m=l("iterator"),v=function(){return this};e.exports=function(e,n,t,l,d,w,y){a(t,n,l);var _,S,E,b=function(e){if(e===d&&C)return C;if(!g&&e in I)return I[e];switch(e){case"keys":case"values":case"entries":return function(){return new t(this,e)}}return function(){return new t(this)}},P=n+" Iterator",A=!1,I=e.prototype,x=I[m]||I["@@iterator"]||d&&I[d],C=!g&&x||b(d),O="Array"==n&&I.entries||x;if(O&&(_=o(O.call(new e)),h!==Object.prototype&&_.next&&(f||o(_)===h||(i?i(_,h):"function"!=typeof _[m]&&c(_,m,v)),s(_,P,!0,!0),f&&(p[P]=v))),"values"==d&&x&&"values"!==x.name&&(A=!0,C=function(){return x.call(this)}),f&&!y||I[m]===C||c(I,m,C),p[n]=C,d)if(S={values:b("values"),keys:w?C:b("keys"),entries:b("entries")},y)for(E in S)(g||A||!(E in I))&&u(I,E,S[E]);else r({target:n,proto:!0,forced:g||A},S);return S}},function(e,n,t){"use strict";var r=t(98).IteratorPrototype,a=t(48),o=t(33),i=t(49),s=t(38),c=function(){return this};e.exports=function(e,n,t){var u=n+" Iterator";return e.prototype=a(r,{next:o(1,t)}),i(e,u,!1,!0),s[u]=c,e}},function(e,n,t){"use strict";var r,a,o,i=t(99),s=t(20),c=t(10),u=t(5),l=t(34),f=u("iterator"),p=!1;[].keys&&("next"in(o=[].keys())?(a=i(i(o)))!==Object.prototype&&(r=a):p=!0),null==r&&(r={}),l||c(r,f)||s(r,f,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:p}},function(e,n,t){var r=t(10),a=t(21),o=t(55),i=t(124),s=o("IE_PROTO"),c=Object.prototype;e.exports=i?Object.getPrototypeOf:function(e){return e=a(e),r(e,s)?e[s]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?c:null}},function(e,n,t){var r=t(11),a=t(16),o=t(9),i=t(74);e.exports=r?Object.defineProperties:function(e,n){o(e);for(var t,r=i(n),s=r.length,c=0;s>c;)a.f(e,t=r[c++],n[t]);return e}},function(e,n,t){"use strict";var r=t(28),a=t(50),o=t(38),i=t(30),s=t(96),c=i.set,u=i.getterFor("Array Iterator");e.exports=s(Array,"Array",(function(e,n){c(this,{type:"Array Iterator",target:r(e),index:0,kind:n})}),(function(){var e=u(this),n=e.target,t=e.kind,r=e.index++;return!n||r>=n.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==t?{value:r,done:!1}:"values"==t?{value:n[r],done:!1}:{value:[r,n[r]],done:!1}}),"values"),o.Arguments=o.Array,a("keys"),a("values"),a("entries")},function(e,n,t){var r=t(6),a=t(5),o=t(34),i=a("iterator");e.exports=!r((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),n=e.searchParams,t="";return e.pathname="c%20d",n.forEach((function(e,r){n.delete("b"),t+=r+e})),o&&!e.toJSON||!n.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==n.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!n[i]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==t||"x"!==new URL("http://x",void 0).host}))},function(e,n){e.exports=function(e,n,t){if(!(e instanceof n))throw TypeError("Incorrect "+(t?t+" ":"")+"invocation");return e}},function(e,n,t){"use strict";var r=t(70),a=t(21),o=t(131),i=t(132),s=t(13),c=t(51),u=t(76);e.exports=function(e){var n,t,l,f,p,d,h=a(e),g="function"==typeof this?this:Array,m=arguments.length,v=m>1?arguments[1]:void 0,w=void 0!==v,y=u(h),_=0;if(w&&(v=r(v,m>2?arguments[2]:void 0,2)),null==y||g==Array&&i(y))for(t=new g(n=s(h.length));n>_;_++)d=w?v(h[_],_):h[_],c(t,_,d);else for(p=(f=y.call(h)).next,t=new g;!(l=p.call(f)).done;_++)d=w?o(f,v,[l.value,_],!0):l.value,c(t,_,d);return t.length=_,t}},function(e,n,t){"use strict";var r=t(7),a=t(6),o=t(72),i=t(12),s=t(21),c=t(13),u=t(51),l=t(71),f=t(52),p=t(5),d=t(106),h=p("isConcatSpreadable"),g=d>=51||!a((function(){var e=[];return e[h]=!1,e.concat()[0]!==e})),m=f("concat"),v=function(e){if(!i(e))return!1;var n=e[h];return void 0!==n?!!n:o(e)};r({target:"Array",proto:!0,forced:!g||!m},{concat:function(e){var n,t,r,a,o,i=s(this),f=l(i,0),p=0;for(n=-1,r=arguments.length;n<r;n++)if(v(o=-1===n?i:arguments[n])){if(p+(a=c(o.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(t=0;t<a;t++,p++)t in o&&u(f,p,o[t])}else{if(p>=9007199254740991)throw TypeError("Maximum allowed index exceeded");u(f,p++,o)}return f.length=p,f}})},function(e,n,t){var r,a,o=t(8),i=t(137),s=o.process,c=s&&s.versions,u=c&&c.v8;u?a=(r=u.split("."))[0]+r[1]:i&&(!(r=i.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=i.match(/Chrome\/(\d+)/))&&(a=r[1]),e.exports=a&&+a},function(e,n,t){"use strict";var r=t(7),a=t(69).find,o=t(50),i=t(24),s=!0,c=i("find");"find"in[]&&Array(1).find((function(){s=!1})),r({target:"Array",proto:!0,forced:s||!c},{find:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}}),o("find")},function(e,n,t){"use strict";var r=t(7),a=t(69).map,o=t(52),i=t(24),s=o("map"),c=i("map");r({target:"Array",proto:!0,forced:!s||!c},{map:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,n,t){"use strict";var r=t(11),a=t(8),o=t(60),i=t(17),s=t(10),c=t(29),u=t(87),l=t(42),f=t(6),p=t(48),d=t(57).f,h=t(40).f,g=t(16).f,m=t(140).trim,v=a.Number,w=v.prototype,y="Number"==c(p(w)),_=function(e){var n,t,r,a,o,i,s,c,u=l(e,!1);if("string"==typeof u&&u.length>2)if(43===(n=(u=m(u)).charCodeAt(0))||45===n){if(88===(t=u.charCodeAt(2))||120===t)return NaN}else if(48===n){switch(u.charCodeAt(1)){case 66:case 98:r=2,a=49;break;case 79:case 111:r=8,a=55;break;default:return+u}for(i=(o=u.slice(2)).length,s=0;s<i;s++)if((c=o.charCodeAt(s))<48||c>a)return NaN;return parseInt(o,r)}return+u};if(o("Number",!v(" 0o1")||!v("0b1")||v("+0x1"))){for(var S,E=function(e){var n=arguments.length<1?0:e,t=this;return t instanceof E&&(y?f((function(){w.valueOf.call(t)})):"Number"!=c(t))?u(new v(_(n)),t,E):_(n)},b=r?d(v):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),P=0;b.length>P;P++)s(v,S=b[P])&&!s(E,S)&&g(E,S,h(v,S));E.prototype=w,w.constructor=E,i(a,"Number",E)}},function(e,n,t){"use strict";var r=t(7),a=t(58).includes,o=t(50);r({target:"Array",proto:!0,forced:!t(24)("indexOf",{ACCESSORS:!0,1:0})},{includes:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}}),o("includes")},function(e,n,t){"use strict";var r=t(7),a=t(92),o=t(15);r({target:"String",proto:!0,forced:!t(93)("includes")},{includes:function(e){return!!~String(o(this)).indexOf(a(e),arguments.length>1?arguments[1]:void 0)}})},function(e,n,t){var r=t(11),a=t(16).f,o=Function.prototype,i=o.toString,s=/^\s*function ([^ (]*)/;r&&!("name"in o)&&a(o,"name",{configurable:!0,get:function(){try{return i.call(this).match(s)[1]}catch(e){return""}}})},function(e,n){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(e){"object"==typeof window&&(t=window)}e.exports=t},function(e,n,t){var r=t(8),a=t(80),o=r.WeakMap;e.exports="function"==typeof o&&/native code/.test(a(o))},function(e,n,t){var r=t(10),a=t(116),o=t(40),i=t(16);e.exports=function(e,n){for(var t=a(n),s=i.f,c=o.f,u=0;u<t.length;u++){var l=t[u];r(e,l)||s(e,l,c(n,l))}}},function(e,n,t){var r=t(35),a=t(57),o=t(85),i=t(9);e.exports=r("Reflect","ownKeys")||function(e){var n=a.f(i(e)),t=o.f;return t?n.concat(t(e)):n}},function(e,n,t){var r=t(8);e.exports=r},function(e,n,t){var r=t(86);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(e,n,t){"use strict";var r=t(62),a=t(63);e.exports=r?{}.toString:function(){return"[object "+a(this)+"]"}},function(e,n,t){var r=t(11),a=t(8),o=t(60),i=t(87),s=t(16).f,c=t(57).f,u=t(64),l=t(65),f=t(89),p=t(17),d=t(6),h=t(30).set,g=t(122),m=t(5)("match"),v=a.RegExp,w=v.prototype,y=/a/g,_=/a/g,S=new v(y)!==y,E=f.UNSUPPORTED_Y;if(r&&o("RegExp",!S||E||d((function(){return _[m]=!1,v(y)!=y||v(_)==_||"/a/i"!=v(y,"i")})))){for(var b=function(e,n){var t,r=this instanceof b,a=u(e),o=void 0===n;if(!r&&a&&e.constructor===b&&o)return e;S?a&&!o&&(e=e.source):e instanceof b&&(o&&(n=l.call(e)),e=e.source),E&&(t=!!n&&n.indexOf("y")>-1)&&(n=n.replace(/y/g,""));var s=i(S?new v(e,n):v(e,n),r?this:w,b);return E&&t&&h(s,{sticky:t}),s},P=function(e){e in b||s(b,e,{configurable:!0,get:function(){return v[e]},set:function(n){v[e]=n}})},A=c(v),I=0;A.length>I;)P(A[I++]);w.constructor=b,b.prototype=w,p(a,"RegExp",b)}g("RegExp")},function(e,n,t){var r=t(12);e.exports=function(e){if(!r(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype");return e}},function(e,n,t){"use strict";var r=t(35),a=t(16),o=t(5),i=t(11),s=o("species");e.exports=function(e){var n=r(e),t=a.f;i&&n&&!n[s]&&t(n,s,{configurable:!0,get:function(){return this}})}},function(e,n,t){var r=t(9),a=t(91),o=t(5)("species");e.exports=function(e,n){var t,i=r(e).constructor;return void 0===i||null==(t=r(i)[o])?n:a(t)}},function(e,n,t){var r=t(6);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},function(e,n,t){var r=t(35);e.exports=r("document","documentElement")},function(e,n,t){"use strict";var r=t(45),a=t(9),o=t(15),i=t(127),s=t(46);r("search",1,(function(e,n,t){return[function(n){var t=o(this),r=null==n?void 0:n[e];return void 0!==r?r.call(n,t):new RegExp(n)[e](String(t))},function(e){var r=t(n,e,this);if(r.done)return r.value;var o=a(e),c=String(this),u=o.lastIndex;i(u,0)||(o.lastIndex=0);var l=s(o,c);return i(o.lastIndex,u)||(o.lastIndex=u),null===l?-1:l.index}]}))},function(e,n){e.exports=Object.is||function(e,n){return e===n?0!==e||1/e==1/n:e!=e&&n!=n}},function(e,n,t){var r=t(8),a=t(95),o=t(101),i=t(20),s=t(5),c=s("iterator"),u=s("toStringTag"),l=o.values;for(var f in a){var p=r[f],d=p&&p.prototype;if(d){if(d[c]!==l)try{i(d,c,l)}catch(e){d[c]=l}if(d[u]||i(d,u,f),a[f])for(var h in o)if(d[h]!==o[h])try{i(d,h,o[h])}catch(e){d[h]=o[h]}}}},function(e,n,t){"use strict";t(75);var r,a=t(7),o=t(11),i=t(102),s=t(8),c=t(100),u=t(17),l=t(103),f=t(10),p=t(130),d=t(104),h=t(67).codeAt,g=t(133),m=t(49),v=t(134),w=t(30),y=s.URL,_=v.URLSearchParams,S=v.getState,E=w.set,b=w.getterFor("URL"),P=Math.floor,A=Math.pow,I=/[A-Za-z]/,x=/[\d+-.A-Za-z]/,C=/\d/,O=/^(0x|0X)/,k=/^[0-7]+$/,M=/^\d+$/,T=/^[\dA-Fa-f]+$/,L=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,D=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,R=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,U=/[\u0009\u000A\u000D]/g,N=function(e,n){var t,r,a;if("["==n.charAt(0)){if("]"!=n.charAt(n.length-1))return"Invalid host";if(!(t=j(n.slice(1,-1))))return"Invalid host";e.host=t}else if(G(e)){if(n=g(n),L.test(n))return"Invalid host";if(null===(t=B(n)))return"Invalid host";e.host=t}else{if(D.test(n))return"Invalid host";for(t="",r=d(n),a=0;a<r.length;a++)t+=H(r[a],F);e.host=t}},B=function(e){var n,t,r,a,o,i,s,c=e.split(".");if(c.length&&""==c[c.length-1]&&c.pop(),(n=c.length)>4)return e;for(t=[],r=0;r<n;r++){if(""==(a=c[r]))return e;if(o=10,a.length>1&&"0"==a.charAt(0)&&(o=O.test(a)?16:8,a=a.slice(8==o?1:2)),""===a)i=0;else{if(!(10==o?M:8==o?k:T).test(a))return e;i=parseInt(a,o)}t.push(i)}for(r=0;r<n;r++)if(i=t[r],r==n-1){if(i>=A(256,5-n))return null}else if(i>255)return null;for(s=t.pop(),r=0;r<t.length;r++)s+=t[r]*A(256,3-r);return s},j=function(e){var n,t,r,a,o,i,s,c=[0,0,0,0,0,0,0,0],u=0,l=null,f=0,p=function(){return e.charAt(f)};if(":"==p()){if(":"!=e.charAt(1))return;f+=2,l=++u}for(;p();){if(8==u)return;if(":"!=p()){for(n=t=0;t<4&&T.test(p());)n=16*n+parseInt(p(),16),f++,t++;if("."==p()){if(0==t)return;if(f-=t,u>6)return;for(r=0;p();){if(a=null,r>0){if(!("."==p()&&r<4))return;f++}if(!C.test(p()))return;for(;C.test(p());){if(o=parseInt(p(),10),null===a)a=o;else{if(0==a)return;a=10*a+o}if(a>255)return;f++}c[u]=256*c[u]+a,2!=++r&&4!=r||u++}if(4!=r)return;break}if(":"==p()){if(f++,!p())return}else if(p())return;c[u++]=n}else{if(null!==l)return;f++,l=++u}}if(null!==l)for(i=u-l,u=7;0!=u&&i>0;)s=c[u],c[u--]=c[l+i-1],c[l+--i]=s;else if(8!=u)return;return c},V=function(e){var n,t,r,a;if("number"==typeof e){for(n=[],t=0;t<4;t++)n.unshift(e%256),e=P(e/256);return n.join(".")}if("object"==typeof e){for(n="",r=function(e){for(var n=null,t=1,r=null,a=0,o=0;o<8;o++)0!==e[o]?(a>t&&(n=r,t=a),r=null,a=0):(null===r&&(r=o),++a);return a>t&&(n=r,t=a),n}(e),t=0;t<8;t++)a&&0===e[t]||(a&&(a=!1),r===t?(n+=t?":":"::",a=!0):(n+=e[t].toString(16),t<7&&(n+=":")));return"["+n+"]"}return e},F={},J=p({},F,{" ":1,'"':1,"<":1,">":1,"`":1}),q=p({},J,{"#":1,"?":1,"{":1,"}":1}),W=p({},q,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),H=function(e,n){var t=h(e,0);return t>32&&t<127&&!f(n,e)?e:encodeURIComponent(e)},K={ftp:21,file:null,http:80,https:443,ws:80,wss:443},G=function(e){return f(K,e.scheme)},$=function(e){return""!=e.username||""!=e.password},z=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},X=function(e,n){var t;return 2==e.length&&I.test(e.charAt(0))&&(":"==(t=e.charAt(1))||!n&&"|"==t)},Y=function(e){var n;return e.length>1&&X(e.slice(0,2))&&(2==e.length||"/"===(n=e.charAt(2))||"\\"===n||"?"===n||"#"===n)},Q=function(e){var n=e.path,t=n.length;!t||"file"==e.scheme&&1==t&&X(n[0],!0)||n.pop()},Z=function(e){return"."===e||"%2e"===e.toLowerCase()},ee={},ne={},te={},re={},ae={},oe={},ie={},se={},ce={},ue={},le={},fe={},pe={},de={},he={},ge={},me={},ve={},we={},ye={},_e={},Se=function(e,n,t,a){var o,i,s,c,u,l=t||ee,p=0,h="",g=!1,m=!1,v=!1;for(t||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,n=n.replace(R,"")),n=n.replace(U,""),o=d(n);p<=o.length;){switch(i=o[p],l){case ee:if(!i||!I.test(i)){if(t)return"Invalid scheme";l=te;continue}h+=i.toLowerCase(),l=ne;break;case ne:if(i&&(x.test(i)||"+"==i||"-"==i||"."==i))h+=i.toLowerCase();else{if(":"!=i){if(t)return"Invalid scheme";h="",l=te,p=0;continue}if(t&&(G(e)!=f(K,h)||"file"==h&&($(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=h,t)return void(G(e)&&K[e.scheme]==e.port&&(e.port=null));h="","file"==e.scheme?l=de:G(e)&&a&&a.scheme==e.scheme?l=re:G(e)?l=se:"/"==o[p+1]?(l=ae,p++):(e.cannotBeABaseURL=!0,e.path.push(""),l=we)}break;case te:if(!a||a.cannotBeABaseURL&&"#"!=i)return"Invalid scheme";if(a.cannotBeABaseURL&&"#"==i){e.scheme=a.scheme,e.path=a.path.slice(),e.query=a.query,e.fragment="",e.cannotBeABaseURL=!0,l=_e;break}l="file"==a.scheme?de:oe;continue;case re:if("/"!=i||"/"!=o[p+1]){l=oe;continue}l=ce,p++;break;case ae:if("/"==i){l=ue;break}l=ve;continue;case oe:if(e.scheme=a.scheme,i==r)e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query=a.query;else if("/"==i||"\\"==i&&G(e))l=ie;else if("?"==i)e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query="",l=ye;else{if("#"!=i){e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.path.pop(),l=ve;continue}e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query=a.query,e.fragment="",l=_e}break;case ie:if(!G(e)||"/"!=i&&"\\"!=i){if("/"!=i){e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,l=ve;continue}l=ue}else l=ce;break;case se:if(l=ce,"/"!=i||"/"!=h.charAt(p+1))continue;p++;break;case ce:if("/"!=i&&"\\"!=i){l=ue;continue}break;case ue:if("@"==i){g&&(h="%40"+h),g=!0,s=d(h);for(var w=0;w<s.length;w++){var y=s[w];if(":"!=y||v){var _=H(y,W);v?e.password+=_:e.username+=_}else v=!0}h=""}else if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&G(e)){if(g&&""==h)return"Invalid authority";p-=d(h).length+1,h="",l=le}else h+=i;break;case le:case fe:if(t&&"file"==e.scheme){l=ge;continue}if(":"!=i||m){if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&G(e)){if(G(e)&&""==h)return"Invalid host";if(t&&""==h&&($(e)||null!==e.port))return;if(c=N(e,h))return c;if(h="",l=me,t)return;continue}"["==i?m=!0:"]"==i&&(m=!1),h+=i}else{if(""==h)return"Invalid host";if(c=N(e,h))return c;if(h="",l=pe,t==fe)return}break;case pe:if(!C.test(i)){if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&G(e)||t){if(""!=h){var S=parseInt(h,10);if(S>65535)return"Invalid port";e.port=G(e)&&S===K[e.scheme]?null:S,h=""}if(t)return;l=me;continue}return"Invalid port"}h+=i;break;case de:if(e.scheme="file","/"==i||"\\"==i)l=he;else{if(!a||"file"!=a.scheme){l=ve;continue}if(i==r)e.host=a.host,e.path=a.path.slice(),e.query=a.query;else if("?"==i)e.host=a.host,e.path=a.path.slice(),e.query="",l=ye;else{if("#"!=i){Y(o.slice(p).join(""))||(e.host=a.host,e.path=a.path.slice(),Q(e)),l=ve;continue}e.host=a.host,e.path=a.path.slice(),e.query=a.query,e.fragment="",l=_e}}break;case he:if("/"==i||"\\"==i){l=ge;break}a&&"file"==a.scheme&&!Y(o.slice(p).join(""))&&(X(a.path[0],!0)?e.path.push(a.path[0]):e.host=a.host),l=ve;continue;case ge:if(i==r||"/"==i||"\\"==i||"?"==i||"#"==i){if(!t&&X(h))l=ve;else if(""==h){if(e.host="",t)return;l=me}else{if(c=N(e,h))return c;if("localhost"==e.host&&(e.host=""),t)return;h="",l=me}continue}h+=i;break;case me:if(G(e)){if(l=ve,"/"!=i&&"\\"!=i)continue}else if(t||"?"!=i)if(t||"#"!=i){if(i!=r&&(l=ve,"/"!=i))continue}else e.fragment="",l=_e;else e.query="",l=ye;break;case ve:if(i==r||"/"==i||"\\"==i&&G(e)||!t&&("?"==i||"#"==i)){if(".."===(u=(u=h).toLowerCase())||"%2e."===u||".%2e"===u||"%2e%2e"===u?(Q(e),"/"==i||"\\"==i&&G(e)||e.path.push("")):Z(h)?"/"==i||"\\"==i&&G(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&X(h)&&(e.host&&(e.host=""),h=h.charAt(0)+":"),e.path.push(h)),h="","file"==e.scheme&&(i==r||"?"==i||"#"==i))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==i?(e.query="",l=ye):"#"==i&&(e.fragment="",l=_e)}else h+=H(i,q);break;case we:"?"==i?(e.query="",l=ye):"#"==i?(e.fragment="",l=_e):i!=r&&(e.path[0]+=H(i,F));break;case ye:t||"#"!=i?i!=r&&("'"==i&&G(e)?e.query+="%27":e.query+="#"==i?"%23":H(i,F)):(e.fragment="",l=_e);break;case _e:i!=r&&(e.fragment+=H(i,J))}p++}},Ee=function(e){var n,t,r=l(this,Ee,"URL"),a=arguments.length>1?arguments[1]:void 0,i=String(e),s=E(r,{type:"URL"});if(void 0!==a)if(a instanceof Ee)n=b(a);else if(t=Se(n={},String(a)))throw TypeError(t);if(t=Se(s,i,null,n))throw TypeError(t);var c=s.searchParams=new _,u=S(c);u.updateSearchParams(s.query),u.updateURL=function(){s.query=String(c)||null},o||(r.href=Pe.call(r),r.origin=Ae.call(r),r.protocol=Ie.call(r),r.username=xe.call(r),r.password=Ce.call(r),r.host=Oe.call(r),r.hostname=ke.call(r),r.port=Me.call(r),r.pathname=Te.call(r),r.search=Le.call(r),r.searchParams=De.call(r),r.hash=Re.call(r))},be=Ee.prototype,Pe=function(){var e=b(this),n=e.scheme,t=e.username,r=e.password,a=e.host,o=e.port,i=e.path,s=e.query,c=e.fragment,u=n+":";return null!==a?(u+="//",$(e)&&(u+=t+(r?":"+r:"")+"@"),u+=V(a),null!==o&&(u+=":"+o)):"file"==n&&(u+="//"),u+=e.cannotBeABaseURL?i[0]:i.length?"/"+i.join("/"):"",null!==s&&(u+="?"+s),null!==c&&(u+="#"+c),u},Ae=function(){var e=b(this),n=e.scheme,t=e.port;if("blob"==n)try{return new URL(n.path[0]).origin}catch(e){return"null"}return"file"!=n&&G(e)?n+"://"+V(e.host)+(null!==t?":"+t:""):"null"},Ie=function(){return b(this).scheme+":"},xe=function(){return b(this).username},Ce=function(){return b(this).password},Oe=function(){var e=b(this),n=e.host,t=e.port;return null===n?"":null===t?V(n):V(n)+":"+t},ke=function(){var e=b(this).host;return null===e?"":V(e)},Me=function(){var e=b(this).port;return null===e?"":String(e)},Te=function(){var e=b(this),n=e.path;return e.cannotBeABaseURL?n[0]:n.length?"/"+n.join("/"):""},Le=function(){var e=b(this).query;return e?"?"+e:""},De=function(){return b(this).searchParams},Re=function(){var e=b(this).fragment;return e?"#"+e:""},Ue=function(e,n){return{get:e,set:n,configurable:!0,enumerable:!0}};if(o&&c(be,{href:Ue(Pe,(function(e){var n=b(this),t=String(e),r=Se(n,t);if(r)throw TypeError(r);S(n.searchParams).updateSearchParams(n.query)})),origin:Ue(Ae),protocol:Ue(Ie,(function(e){var n=b(this);Se(n,String(e)+":",ee)})),username:Ue(xe,(function(e){var n=b(this),t=d(String(e));if(!z(n)){n.username="";for(var r=0;r<t.length;r++)n.username+=H(t[r],W)}})),password:Ue(Ce,(function(e){var n=b(this),t=d(String(e));if(!z(n)){n.password="";for(var r=0;r<t.length;r++)n.password+=H(t[r],W)}})),host:Ue(Oe,(function(e){var n=b(this);n.cannotBeABaseURL||Se(n,String(e),le)})),hostname:Ue(ke,(function(e){var n=b(this);n.cannotBeABaseURL||Se(n,String(e),fe)})),port:Ue(Me,(function(e){var n=b(this);z(n)||(""==(e=String(e))?n.port=null:Se(n,e,pe))})),pathname:Ue(Te,(function(e){var n=b(this);n.cannotBeABaseURL||(n.path=[],Se(n,e+"",me))})),search:Ue(Le,(function(e){var n=b(this);""==(e=String(e))?n.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),n.query="",Se(n,e,ye)),S(n.searchParams).updateSearchParams(n.query)})),searchParams:Ue(De),hash:Ue(Re,(function(e){var n=b(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),n.fragment="",Se(n,e,_e)):n.fragment=null}))}),u(be,"toJSON",(function(){return Pe.call(this)}),{enumerable:!0}),u(be,"toString",(function(){return Pe.call(this)}),{enumerable:!0}),y){var Ne=y.createObjectURL,Be=y.revokeObjectURL;Ne&&u(Ee,"createObjectURL",(function(e){return Ne.apply(y,arguments)})),Be&&u(Ee,"revokeObjectURL",(function(e){return Be.apply(y,arguments)}))}m(Ee,"URL"),a({global:!0,forced:!i,sham:!o},{URL:Ee})},function(e,n,t){"use strict";var r=t(11),a=t(6),o=t(74),i=t(85),s=t(77),c=t(21),u=t(41),l=Object.assign,f=Object.defineProperty;e.exports=!l||a((function(){if(r&&1!==l({b:1},l(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},n={},t=Symbol();return e[t]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),7!=l({},e)[t]||"abcdefghijklmnopqrst"!=o(l({},n)).join("")}))?function(e,n){for(var t=c(e),a=arguments.length,l=1,f=i.f,p=s.f;a>l;)for(var d,h=u(arguments[l++]),g=f?o(h).concat(f(h)):o(h),m=g.length,v=0;m>v;)d=g[v++],r&&!p.call(h,d)||(t[d]=h[d]);return t}:l},function(e,n,t){var r=t(9);e.exports=function(e,n,t,a){try{return a?n(r(t)[0],t[1]):n(t)}catch(n){var o=e.return;throw void 0!==o&&r(o.call(e)),n}}},function(e,n,t){var r=t(5),a=t(38),o=r("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(a.Array===e||i[o]===e)}},function(e,n,t){"use strict";var r=/[^\0-\u007E]/,a=/[.\u3002\uFF0E\uFF61]/g,o="Overflow: input needs wider integers to process",i=Math.floor,s=String.fromCharCode,c=function(e){return e+22+75*(e<26)},u=function(e,n,t){var r=0;for(e=t?i(e/700):e>>1,e+=i(e/n);e>455;r+=36)e=i(e/35);return i(r+36*e/(e+38))},l=function(e){var n,t,r=[],a=(e=function(e){for(var n=[],t=0,r=e.length;t<r;){var a=e.charCodeAt(t++);if(a>=55296&&a<=56319&&t<r){var o=e.charCodeAt(t++);56320==(64512&o)?n.push(((1023&a)<<10)+(1023&o)+65536):(n.push(a),t--)}else n.push(a)}return n}(e)).length,l=128,f=0,p=72;for(n=0;n<e.length;n++)(t=e[n])<128&&r.push(s(t));var d=r.length,h=d;for(d&&r.push("-");h<a;){var g=2147483647;for(n=0;n<e.length;n++)(t=e[n])>=l&&t<g&&(g=t);var m=h+1;if(g-l>i((2147483647-f)/m))throw RangeError(o);for(f+=(g-l)*m,l=g,n=0;n<e.length;n++){if((t=e[n])<l&&++f>2147483647)throw RangeError(o);if(t==l){for(var v=f,w=36;;w+=36){var y=w<=p?1:w>=p+26?26:w-p;if(v<y)break;var _=v-y,S=36-y;r.push(s(c(y+_%S))),v=i(_/S)}r.push(s(c(v))),p=u(f,m,h==d),f=0,++h}}++f,++l}return r.join("")};e.exports=function(e){var n,t,o=[],i=e.toLowerCase().replace(a,".").split(".");for(n=0;n<i.length;n++)t=i[n],o.push(r.test(t)?"xn--"+l(t):t);return o.join(".")}},function(e,n,t){"use strict";t(101);var r=t(7),a=t(35),o=t(102),i=t(17),s=t(135),c=t(49),u=t(97),l=t(30),f=t(103),p=t(10),d=t(70),h=t(63),g=t(9),m=t(12),v=t(48),w=t(33),y=t(136),_=t(76),S=t(5),E=a("fetch"),b=a("Headers"),P=S("iterator"),A=l.set,I=l.getterFor("URLSearchParams"),x=l.getterFor("URLSearchParamsIterator"),C=/\+/g,O=Array(4),k=function(e){return O[e-1]||(O[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},M=function(e){try{return decodeURIComponent(e)}catch(n){return e}},T=function(e){var n=e.replace(C," "),t=4;try{return decodeURIComponent(n)}catch(e){for(;t;)n=n.replace(k(t--),M);return n}},L=/[!'()~]|%20/g,D={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},R=function(e){return D[e]},U=function(e){return encodeURIComponent(e).replace(L,R)},N=function(e,n){if(n)for(var t,r,a=n.split("&"),o=0;o<a.length;)(t=a[o++]).length&&(r=t.split("="),e.push({key:T(r.shift()),value:T(r.join("="))}))},B=function(e){this.entries.length=0,N(this.entries,e)},j=function(e,n){if(e<n)throw TypeError("Not enough arguments")},V=u((function(e,n){A(this,{type:"URLSearchParamsIterator",iterator:y(I(e).entries),kind:n})}),"Iterator",(function(){var e=x(this),n=e.kind,t=e.iterator.next(),r=t.value;return t.done||(t.value="keys"===n?r.key:"values"===n?r.value:[r.key,r.value]),t})),F=function(){f(this,F,"URLSearchParams");var e,n,t,r,a,o,i,s,c,u=arguments.length>0?arguments[0]:void 0,l=this,d=[];if(A(l,{type:"URLSearchParams",entries:d,updateURL:function(){},updateSearchParams:B}),void 0!==u)if(m(u))if("function"==typeof(e=_(u)))for(t=(n=e.call(u)).next;!(r=t.call(n)).done;){if((i=(o=(a=y(g(r.value))).next).call(a)).done||(s=o.call(a)).done||!o.call(a).done)throw TypeError("Expected sequence with length 2");d.push({key:i.value+"",value:s.value+""})}else for(c in u)p(u,c)&&d.push({key:c,value:u[c]+""});else N(d,"string"==typeof u?"?"===u.charAt(0)?u.slice(1):u:u+"")},J=F.prototype;s(J,{append:function(e,n){j(arguments.length,2);var t=I(this);t.entries.push({key:e+"",value:n+""}),t.updateURL()},delete:function(e){j(arguments.length,1);for(var n=I(this),t=n.entries,r=e+"",a=0;a<t.length;)t[a].key===r?t.splice(a,1):a++;n.updateURL()},get:function(e){j(arguments.length,1);for(var n=I(this).entries,t=e+"",r=0;r<n.length;r++)if(n[r].key===t)return n[r].value;return null},getAll:function(e){j(arguments.length,1);for(var n=I(this).entries,t=e+"",r=[],a=0;a<n.length;a++)n[a].key===t&&r.push(n[a].value);return r},has:function(e){j(arguments.length,1);for(var n=I(this).entries,t=e+"",r=0;r<n.length;)if(n[r++].key===t)return!0;return!1},set:function(e,n){j(arguments.length,1);for(var t,r=I(this),a=r.entries,o=!1,i=e+"",s=n+"",c=0;c<a.length;c++)(t=a[c]).key===i&&(o?a.splice(c--,1):(o=!0,t.value=s));o||a.push({key:i,value:s}),r.updateURL()},sort:function(){var e,n,t,r=I(this),a=r.entries,o=a.slice();for(a.length=0,t=0;t<o.length;t++){for(e=o[t],n=0;n<t;n++)if(a[n].key>e.key){a.splice(n,0,e);break}n===t&&a.push(e)}r.updateURL()},forEach:function(e){for(var n,t=I(this).entries,r=d(e,arguments.length>1?arguments[1]:void 0,3),a=0;a<t.length;)r((n=t[a++]).value,n.key,this)},keys:function(){return new V(this,"keys")},values:function(){return new V(this,"values")},entries:function(){return new V(this,"entries")}},{enumerable:!0}),i(J,P,J.entries),i(J,"toString",(function(){for(var e,n=I(this).entries,t=[],r=0;r<n.length;)e=n[r++],t.push(U(e.key)+"="+U(e.value));return t.join("&")}),{enumerable:!0}),c(F,"URLSearchParams"),r({global:!0,forced:!o},{URLSearchParams:F}),o||"function"!=typeof E||"function"!=typeof b||r({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var n,t,r,a=[e];return arguments.length>1&&(m(n=arguments[1])&&(t=n.body,"URLSearchParams"===h(t)&&((r=n.headers?new b(n.headers):new b).has("content-type")||r.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),n=v(n,{body:w(0,String(t)),headers:w(0,r)}))),a.push(n)),E.apply(this,a)}}),e.exports={URLSearchParams:F,getState:I}},function(e,n,t){var r=t(17);e.exports=function(e,n,t){for(var a in n)r(e,a,n[a],t);return e}},function(e,n,t){var r=t(9),a=t(76);e.exports=function(e){var n=a(e);if("function"!=typeof n)throw TypeError(String(e)+" is not iterable");return r(n.call(e))}},function(e,n,t){var r=t(35);e.exports=r("navigator","userAgent")||""},function(e,n,t){var r=t(7),a=t(139),o=t(50);r({target:"Array",proto:!0},{fill:a}),o("fill")},function(e,n,t){"use strict";var r=t(21),a=t(43),o=t(13);e.exports=function(e){for(var n=r(this),t=o(n.length),i=arguments.length,s=a(i>1?arguments[1]:void 0,t),c=i>2?arguments[2]:void 0,u=void 0===c?t:a(c,t);u>s;)n[s++]=e;return n}},function(e,n,t){var r=t(15),a="["+t(141)+"]",o=RegExp("^"+a+a+"*"),i=RegExp(a+a+"*$"),s=function(e){return function(n){var t=String(r(n));return 1&e&&(t=t.replace(o,"")),2&e&&(t=t.replace(i,"")),t}};e.exports={start:s(1),end:s(2),trim:s(3)}},function(e,n){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},function(e,n,t){var r=t(7),a=t(104);r({target:"Array",stat:!0,forced:!t(143)((function(e){Array.from(e)}))},{from:a})},function(e,n,t){var r=t(5)("iterator"),a=!1;try{var o=0,i={next:function(){return{done:!!o++}},return:function(){a=!0}};i[r]=function(){return this},Array.from(i,(function(){throw 2}))}catch(e){}e.exports=function(e,n){if(!n&&!a)return!1;var t=!1;try{var o={};o[r]=function(){return{next:function(){return{done:t=!0}}}},e(o)}catch(e){}return t}},function(e,n,t){"use strict";var r=t(7),a=t(12),o=t(72),i=t(43),s=t(13),c=t(28),u=t(51),l=t(5),f=t(52),p=t(24),d=f("slice"),h=p("slice",{ACCESSORS:!0,0:0,1:2}),g=l("species"),m=[].slice,v=Math.max;r({target:"Array",proto:!0,forced:!d||!h},{slice:function(e,n){var t,r,l,f=c(this),p=s(f.length),d=i(e,p),h=i(void 0===n?p:n,p);if(o(f)&&("function"!=typeof(t=f.constructor)||t!==Array&&!o(t.prototype)?a(t)&&null===(t=t[g])&&(t=void 0):t=void 0,t===Array||void 0===t))return m.call(f,d,h);for(r=new(void 0===t?Array:t)(v(h-d,0)),l=0;d<h;d++,l++)d in f&&u(r,l,f[d]);return r.length=l,r}})},function(e,n,t){"use strict";var r=t(7),a=t(43),o=t(36),i=t(13),s=t(21),c=t(71),u=t(51),l=t(52),f=t(24),p=l("splice"),d=f("splice",{ACCESSORS:!0,0:0,1:2}),h=Math.max,g=Math.min;r({target:"Array",proto:!0,forced:!p||!d},{splice:function(e,n){var t,r,l,f,p,d,m=s(this),v=i(m.length),w=a(e,v),y=arguments.length;if(0===y?t=r=0:1===y?(t=0,r=v-w):(t=y-2,r=g(h(o(n),0),v-w)),v+t-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(l=c(m,r),f=0;f<r;f++)(p=w+f)in m&&u(l,f,m[p]);if(l.length=r,t<r){for(f=w;f<v-r;f++)d=f+t,(p=f+r)in m?m[d]=m[p]:delete m[d];for(f=v;f>v-r+t;f--)delete m[f-1]}else if(t>r)for(f=v-r;f>w;f--)d=f+t-1,(p=f+r-1)in m?m[d]=m[p]:delete m[d];for(f=0;f<t;f++)m[f+w]=arguments[f+2];return m.length=v-r+t,l}})},function(e,n,t){"use strict";t.r(n),t.d(n,"ms",(function(){return G}));var r=t(3),a=t(0),o=(t(4),t(2),t(1));function i(e){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(n,t){G.isEdop()?o.a.exec("auth",{scope:e.scope,action:e.action?e.action:""},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)})):a.a.appVersion()>=9.06?o.a.exec("authH5",{scope:e.scope,action:e.action?e.action:""},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)})):n()})):new Promise((function(e){e()}))}var s=t(14);t(22),t(112),t(23);function c(){return!a.a.isInApp()||(a.a.appVersion()>=8?o.a.execSync("isLogin"):!!a.a.getCookieFromDocument("ecs_token"))}var u=[],l=[];function f(){if(a.a.isInApp()&&a.a.appVersion()>=8)return o.a.execSync("getClientInfo");return{status:"10",msg:"当前环境不支持getClientInfoSync方法"}}var p={getNetAccessCode:[{businessId:"unicom_h5_001",businessName:"超级星期五"},{businessId:"unicom_h5_002",businessName:"扫码取号"},{businessId:"unicom_h5_003",businessName:"流量包"},{businessId:"unicom_h5_004",businessName:"停机保号"},{businessId:"unicom_h5_ecsrrz",businessName:"二次实人认证"}]};t(68);function d(e,n){return a.a.isInApp()?new Promise((function(t,r){o.a.exec(n,e,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}))})):(console.warn("端外不支持调用"+n+"方法。"),new Promise((function(e,t){var r=new Error;r.data="端外不支持调用"+n+"方法。",t(r)})))}function h(e){var n=event||window.event,t=(null==n?void 0:n.target)||(null==n?void 0:n.srcElement);if(!(a.a.isAndroid()&&t&&null!=t&&t.getAttribute&&"yes"===(null==t?void 0:t.getAttribute("sinovaReduceExpenditure")))){a.a.isAndroid()&&t&&((null==t?void 0:t.setAttribute)&&(null==t||t.setAttribute("sinovaReduceExpenditure","yes")),setTimeout((function(){(null==t?void 0:t.removeAttribute)&&(null==t||t.removeAttribute("sinovaReduceExpenditure"))}),300));var r=e.isNeedLogin;return!0!==r&&(r=!1),!0!==e.navigationBarHidden&&(e.navigationBarHidden=!1),a.a.isInApp()?a.a.appVersion()>=7.06?d(e,"navigateTo"):new Promise((function(n,t){var r={type:"openNewWindow",msg:{title:"",backUrl:"",isReload:"N"},url:e.target};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(r))):a.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(r))})):new Promise((function(n,t){console.warn("端外调用navigateTo接口，只会接收target参数并简单的打开一个网页。"),e.target.startsWith("http")&&(window.location.href=e.target)}))}}function g(){if(a.a.isInApp()){var e={type:"close"};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(e))):a.a.isAndroid()?window.js_invoke.interact(JSON.stringify(e)):a.a.isHarmony()&&o.a.execSync("navigateClose")}else console.warn("端外不支持调用navigateClose方法。")}function m(e,n){return new Promise((function(t,r){o.a.exec(n,e,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}))}))}var v=[],w=[];var y=0;function _(){setTimeout((function(){if(a.a.isInApp()){var e=document.getElementsByTagName("html")[0].offsetHeight;if(e==y)return;y=e,o.a.exec("ListenWebViewHeight",{height:e})}}),50)}t(27);var S=document.title;window.clientGetJsTitle=function(){return S};var E={setBounces:function(e){a.a.isIOS()&&a.a.isInApp()&&o.a.exec("webViewAction",{bounces:e})},setTitle:function(e){S=e}};var b=t(18),P=t(19),A=function(){function e(){var n=this;Object(b.a)(this,e);o.a.exec("MusicPlayer",{type:"createPlayer"},(function(e){if(console.log("res:",e),null!=e){var t=e.state,r=e.msg;switch(console.log("state:",t),t){case"playing":n.onPlaying(r);break;case"pause":n.onPause(r);break;case"playEnd":n.onPlayEnd(r);break;case"fail":n.onLoadFail(r);break;case"mediaListEnd":n.onListPlayEnd();break;case"iconClick":n.onPlayBrandIconClick(r);break;case"closeClick":n.onPlayBrandCloseClick(r)}}}),(function(e){console.debug("播放器创建异常:",e)}),!0)}return Object(P.a)(e,[{key:"addAudioToPlayer",value:function(e){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new Promise((function(n,t){var r={type:"addMusic",data:e};o.a.exec("MusicPlayer",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}));console.warn("当前环境不支持调用addAudioToPlayer。")}},{key:"startAudioPlay",value:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new Promise((function(e,n){o.a.exec("MusicPlayer",{type:"startPlay"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}));console.warn("当前环境不支持调用startAudioPlay。")}},{key:"stopAudioPlay",value:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new Promise((function(e,n){o.a.exec("MusicPlayer",{type:"stopPlay"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}));console.warn("当前环境不支持调用stopAudioPlay。")}},{key:"onPlaying",value:function(e){}},{key:"onPause",value:function(e){}},{key:"onPlayEnd",value:function(e){}},{key:"onLoadFail",value:function(e){}},{key:"onListPlayEnd",value:function(){}},{key:"onPlayBrandIconClick",value:function(e){}},{key:"onPlayBrandCloseClick",value:function(e){}},{key:"removeAudio",value:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new Promise((function(e,n){o.a.exec("MusicPlayer",{type:"removeMusics"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}));console.warn("当前环境不支持调用removeAudio。")}},{key:"removeAudioPlayer",value:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new Promise((function(e,n){o.a.exec("MusicPlayer",{type:"removePlayer"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}));console.warn("当前环境不支持调用removeAudioPlayer。")}}]),e}();function I(e){var n="";switch(e.mnc){case 0:n="ChinaMobile";break;case 1:n="ChinaUnicom";break;case 2:n="ChinaMobile";break;case 3:n="ChinaTelecom";break;case 4:n="ChinaMobile";break;case 5:n="ChinaTelecom";break;case 6:n="ChinaUnicom";break;case 7:case 8:n="ChinaMobile";break;case 9:case 10:n="ChinaUnicom";break;case 11:case 12:n="ChinaTelecom";break;case 13:n="ChinaMobile";break;default:n=""}return e.carrier=n,e.errorMsg="无法读取手机网络信息，请检查手机设备访问权限是否开启，如果您是小米手机用户，请检查是否设置了空白通行证。",e}function x(){var e=a.a.appVersion()>=10.06&&a.a.isIOS(),n=a.a.appVersion()>=10.07&&a.a.isAndroid();return a.a.isInApp()&&(n||e)?o.a.execSync("UIAccessibilityManager",{type:"getStatus"}):(console.log("端外暂不支持UIAccessibilityManager方法"),!1)}var C=function(){function e(n){Object(b.a)(this,e),this.initAttribute(n)}return Object(P.a)(e,[{key:"initAttribute",value:function(e){this.mainTitle=e.mainTitle,this.pkgName=e.pkgName,this.downloadUrl=e.downloadUrl,this.totalLength=e.totalLength,this.readableTotalLength=e.readableTotalLength,this.totalOffset=e.totalOffset,this.readableTotalOffset=e.readableTotalOffset,this.speed=e.speed,this.taskStatus=e.taskStatus,this.errorMsg=e.errorMsg,this.remark=null}},{key:"onTaskStart",value:function(){}},{key:"onTaskEnd",value:function(){}},{key:"onProgressUpdate",value:function(){}},{key:"onError",value:function(){}},{key:"startDownload",value:function(){var e=this;if(console.log("》》》开始下载",this),a.a.isInApp())o.a.exec("gameDownloadTaskExecution",{type:"startDownload",originData:this},(function(n){var t=n.data;e.initAttribute(t),"taskStart"==n.action?e.onTaskStart():"progressUpdate"==n.action?e.onProgressUpdate():"taskEnd"==n.action&&e.onTaskEnd()}),(function(n){var t=new Error;t.data=n,e.onError(t)}),!0);else{var n=new Error;n.data="端外不支持游戏分发接口调用",this.onError(n)}}},{key:"stopDownload",value:function(){var e=this;return a.a.isInApp()?(console.log("》》》停止下载",this),new Promise((function(n,t){o.a.exec("gameDownloadTaskExecution",{type:"stopDownload",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"delete",value:function(){var e=this;return a.a.isInApp()?(console.log("》》》删除下载任务和文件",this),new Promise((function(n,t){o.a.exec("gameDownloadTaskExecution",{type:"delete",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"installGame",value:function(e){var n=this;return a.a.isInApp?new Promise((function(t,r){o.a.exec("gameDownloadTaskExecution",{type:"installGame",autoDeletePkg:e,originData:n},(function(){t()}),(function(e){var n=new Error;n.data=e,r(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"openGame",value:function(){var e=this;return a.a.isInApp?new Promise((function(n,t){o.a.exec("gameDownloadTaskExecution",{type:"openGame",originData:e},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持游戏分发接口调用",n(t)}))}},{key:"isInstalled",value:function(){return!!a.a.isInApp&&o.a.execSync("gameDownloadTaskExecution",{type:"isInstalled",originData:this})}}]),e}();var O=function(){function e(n){Object(b.a)(this,e),this.initAttribute(n)}return Object(P.a)(e,[{key:"initAttribute",value:function(e){this.filePath=e.filePath,this.tempFilePath=e.tempFilePath,this.url=e.url,this.totalLength=e.totalLength,this.totalOffset=e.totalOffset,this.speed=e.speed,this.taskStatus=e.taskStatus,this.errorMsg=e.errorMsg}},{key:"onTaskStart",value:function(){}},{key:"onTaskEnd",value:function(){}},{key:"onProgressUpdate",value:function(){}},{key:"onError",value:function(){}},{key:"startDownload",value:function(){var e=this;if(a.a.isInApp()&&a.a.appVersion()>=8.09)console.log("startDownload-开始下载",this),o.a.exec("downloadFile",{type:"startDownload",url:this.url},(function(n){var t=n.data;e.initAttribute(t),"taskStart"==n.action?e.onTaskStart():"progressUpdate"==n.action?e.onProgressUpdate():"taskEnd"==n.action&&e.onTaskEnd()}),(function(n){var t=new Error;t.data=n,e.onError(t)}),!0);else{var n=new Error;n.data="不支持调用startDownload方法。",this.onError(n)}}},{key:"stopDownload",value:function(e){var n=this;return a.a.isInApp()&&a.a.appVersion()>=8.09?(console.log("stopDownload-停止下载",this),new Promise((function(t,r){var a="no";e&&e.reqTempFilePath&&"yes"==e.reqTempFilePath&&(a="yes"),o.a.exec("downloadFile",{type:"stopDownload",url:n.url,reqTempFilePath:a},(function(e){if(e&&e.action&&"taskEnd"==e.action){var r=e.data;n.initAttribute(r),n.onTaskEnd()}t()}),(function(e){var t=new Error;t.data=e,n.onError(t),r(t)}))}))):new Promise((function(e,t){var r=new Error;r.data="不支持调用stopDownload方法。",n.onError(r),t(r)}))}},{key:"deleteTask",value:function(){var e=this;return a.a.isInApp()&&a.a.appVersion()>=8.09?(console.log("deleteTask-删除任务",this),new Promise((function(n,t){o.a.exec("downloadFile",{type:"deleteTask",url:e.url},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="不支持调用deleteTask方法。",n(t)}))}},{key:"openFile",value:function(){var e=this;return a.a.isInApp()&&a.a.appVersion()>=8.09?(console.log("openFile-删除任务",this),new Promise((function(n,t){o.a.exec("downloadFile",{type:"openFile",url:e.url},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))}))):new Promise((function(e,n){var t=new Error;t.data="不支持调用openFile方法。",n(t)}))}}]),e}();t(109);var k=function(){function e(n){Object(b.a)(this,e),this.uploadId=n}return Object(P.a)(e,[{key:"onComplete",value:function(){}},{key:"onProgressUpdate",value:function(){}},{key:"onError",value:function(){}},{key:"onCancel",value:function(){}},{key:"stopUpload",value:function(){if(a.a.isInApp()&&a.a.appVersion()>=8.09)console.log("stopUpload-停止上传",this),o.a.exec("uploadFile",{type:"stopUpload",uploadId:this.uploadId});else{var e=new Error;e.data="不支持调用stopUpload方法。",this.onError(e)}}}]),e}();t(31);function M(e){if(a.a.isInApp()){if(a.a.appVersion()>=8.0803){var n="yes";return!0===e&&(n="no"),o.a.execSync("getClipboardData",{clear:n})}console.warn("此版本不支持调用getClipboardData方法。")}else console.warn("端外不支持调用getClipboardData方法。")}var T=[],L=[];var D=0;var R=t(26);function U(){var e=this;this.resolve=null,this.reject=null,this.endBlock=null,this.successData=null,this.failData=null,this.isKeep=!0,this.then=function(n){return e.resolve=n,e.successData&&setTimeout((function(){e.resolve(e.successData)}),0),e},this.catch=function(n){return e.reject=n,e.failData&&setTimeout((function(){e.reject(e.failData)}),0),e},this.finally=function(n){return e.endBlock=n,0==e.isKeep&&setTimeout((function(){e.endBlock()}),0),e}}t(105),t(110),t(53),t(144),t(145),t(37),t(111),t(47);var N=[],B=[];function j(e,n){if(a.a.isInApp()&&a.a.appVersion()>=9.02)o.a.exec("OMApi",Object(r.a)({type:"getSimAllInfoOnExec"},e),(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!0);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持openAndTransmitSim方法"},n(t)}}function V(e,n){J("A00000015143525300").then((function(){W({apdu:"80F24000084F005C044F9F70A6",sw:""}).then((function(t){N.push(t),function e(n,t,r){o.a.exec("OMApi",{type:"transmitApduOnExec",apduList:[n]},(function(a){var o=a[0];"6310"==o.resSw?(N.push(o),e(n,t,r)):"9000"==o.resSw?(N.push(o),t(N)):r({status:12,message:"期望值不是9000或6310"})}),(function(e){var n=new Error;n.data=e,r(n)}))}({apdu:"80F24001084F005C044F9F70A6",sw:""},(function(t){q();for(var r=[],a=0;a<t.length;a++){var o=t[a];r.push(o.resRapdu)}for(var i=r.join(""),s=[],c=i.split("61").length,u=0;u<c;u++){var l=i.substring(2,4),f=parseInt(l,16),p=i.substring(0,4+2*f);if(console.log("aidString",p),"61"!=p.substring(0,2))break;i=i.substring(4+2*f,i.length),s.push(p.toUpperCase())}var d=[];s.forEach((function(e){if(e.includes("4F")&&e.includes("A6")&&e.includes("9F70")&&e.includes("E7020101")){console.log("公交卡1",e);var n=parseInt(e.substring(6,8),16),t=e.substring(0,8+2*n+10);"01"==t.substring(t.length-2,t.length)&&(console.log("公交卡1包含01",e),d.push(e))}})),0==d.length&&s.forEach((function(e){(function(e){if("A000000632010105"!=e.substring(8,e.length).toLocaleUpperCase().substring(0,"A000000632010105".length))return!1;var n=parseInt(e.substring(6,8),16),t=e.substring(0,8+2*n+10);if("01"==t.substring(t.length-2,t.length))return!0;if("00"==t.substring(t.length-2,t.length))return!1;return!1})(e)&&(console.log("公交卡2",e),d.push(e))}));var h="";console.log("card0",h),d.forEach((function(e){var n=e.split("A6");if("9F70020701"==n[0].substring(n[0].length-"9F70020701".length,n[0].length)){var t=parseInt(n[0].substring(6,8),16);h=n[0].substring(8,2*t+8)}})),0==h.length?n({status:"13",msg:"没有设置默认卡"}):e({cardAid:h}),N=[]}),(function(e){q(),N=[];var t=new Error;t.data=e,n(t)}))})).catch((function(e){N=[];var t=new Error;t.data=e,n(t)}))})).catch((function(e){var t=new Error;t.data=e,n(t)}))}function F(e){e.splice(0,1);var n=[];console.log("instructionArray对象",e);for(var t=0;t<e.length;t++){var r=e[t],a=r.resSw,o=r.resRapdu;if(""==o||"9000"!=a||o.includes("0000000000000000000000000000000000000000000000"))break;var i=o.substring(0,4),s=parseInt(o.substring(10,18),16),c=o.substring(18,20),u=o.substring(32,40),l=o.substring(40,46);n.push({tradeNo:i,tradeMoney:s,tradeType:c,tradeDate:u,tradeTime:l})}return n}function J(e){return a.a.appVersion()>=9.02&&a.a.isInApp()?new Promise((function(n,t){o.a.exec("OMApi",{type:"openChannelOnExec",aid:e},(function(e){"0000"==e[0].status&&"9000"==e[0].resSw?n():t(e[0])}),(function(e){var n=new Error;n.data=e,t(n)}),!0)})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持开通道方法"},n(t)}))}function q(){return a.a.appVersion()>=9.02&&a.a.isInApp()?new Promise((function(e,n){o.a.exec("OMApi",{type:"closeChannelOnExec"},(function(n){"0000"==n[0].status?e():e(n[0])}),(function(e){console.log("关闭通道失败",e);var t=new Error;t.data=e,n(t)}),!0)})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持开通道方法"},n(t)}))}function W(e){return a.a.appVersion()>=9.02&&a.a.isInApp()?new Promise((function(n,t){o.a.exec("OMApi",{type:"transmitApduOnExec",apduList:[e]},(function(e){n(e[0])}),(function(e){var n=new Error;n.data=e,t(n)}),!0)})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持开通道方法"},n(t)}))}var H=t(39);var K={enableHuidu:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.03)if(XMLHttpRequest&&XMLHttpRequest.toString().indexOf("[native code]")>-1&&XMLHttpRequest.prototype&&XMLHttpRequest.prototype.toString().indexOf("[object XMLHttpRequest]")>-1){var e="",n=XMLHttpRequest.prototype,t=n.send,r=n.open;n.open=function(){return e=arguments.length>1?arguments[1]:"",console.log("灰度ajax hook url=",e),r.apply(this,arguments)},n.send=function(){if(window&&window.__xhr__huidulanjie_headercache)console.log("灰度ajax hook 从缓存中取出header:",window.__xhr__huidulanjie_headercache),this.setRequestHeader("grayflag",window.__xhr__huidulanjie_headercache);else{var n=o.a.execSync("huidu",{type:"getHeaders",url:e});n&&(window.__xhr__huidulanjie_headercache=n,console.log("灰度ajax hook 调用JS接口取出header:",n),this.setRequestHeader("grayflag",n))}return t.apply(this,arguments)}}else console.warn("灰度 原始XMLHttpRequest可能被重写，建议把enableHuidu调用时机尽量前置")}};var G=Object(r.a)(Object(r.a)({},a.a),{},{setEnableDebug:function(e){return a.a.isInApp()&&a.a.appVersion()>8.0902?o.a.execSync("setEnableDebug",Object(r.a)({},e)):""},auth:i,openAuthSetting:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("openAuthSetting",{},(function(){e()}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持openAuthSetting方法"},n(t)}))},registerH5:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("registerH5",e,(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e){e()}))},createDesktopShortcut:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.01?new Promise((function(n,t){o.a.exec("createDesktopShortcut",Object(r.a)({},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持createDesktopShortcut方法"},n(t)}))},showShareMenu:R.c,updateClientMoreMenu:R.d,updateClientMoreMenuFromID:R.e,shareSingleChannel:R.b,openWxMiniProgram:R.a,updateEdopShareMenu:R.f,shake:function(e,n,t){if(a.a.isInApp())o.a.exec("shake",Object(r.a)({type:"start"},e),(function(e){if(console.log("MSJsBridge-shake",e),e&&e.status){if("00"==e.status)return void n({status:"00",msg:"注册成功"});if("01"==e.status)return void n({status:"01",msg:"摇动了手机"});if("10"==e.status)return void n({status:"10",msg:"长时间没有摇动手机时自动关闭服务"});if("12"==e.status)return void n({status:"12",msg:"手动关闭服务"})}var r=new Error;r.data={status:"11",msg:"程序错误：客户端没有返回状态"},t(r)}),(function(e){if(console.error("MSJsBridge-shake",e.data),e&&e.status){if("10"==e.status)return void n({status:"10",msg:"长时间没有摇动手机时自动关闭服务"});if("12"==e.status)return void n({status:"12",msg:"手动关闭服务"})}var r=new Error;r.data=e,t(r)}),!0);else{var i=new Error;i.data={status:"11",msg:"当前环境不支持shake方法"},t(i)}},stopShake:function(){a.a.isInApp()&&o.a.exec("shake",{type:"stop"},null,null,!0)},startAccelerometer:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("accelerometer",{type:"start"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持startAccelerometer方法"},n(t)}))},stopAccelerometer:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("accelerometer",{type:"stop"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持stopAccelerometer方法"},n(t)}))},onAccelerometerChange:function(e){return o.a.addEventListener("onAccelerometerSensorChanged",e)},offAccelerometerChange:function(e){o.a.removeEventListener("onAccelerometerSensorChanged",e)},startCompass:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("compass",{type:"start"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持startCompass方法"},n(t)}))},stopCompass:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("compass",{type:"stop"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持stopCompass方法"},n(t)}))},onCompassChange:function(e){return o.a.addEventListener("onCompassSensorChanged",e)},offCompassChange:function(e){o.a.removeEventListener("onCompassSensorChanged",e)},startGyroscope:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("gyroscope",{type:"start"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持startGyroscope方法"},n(t)}))},stopGyroscope:function(){return a.a.isInApp()&&a.a.appVersion()>=9?new Promise((function(e,n){o.a.exec("gyroscope",{type:"stop"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持stopGyroscope方法"},n(t)}))},onGyroscopeChange:function(e){return o.a.addEventListener("onGyroscopeSensorChanged",e)},offGyroscopeChange:function(e){o.a.removeEventListener("onGyroscopeSensorChanged",e)},scanCode:function(){return a.a.isInApp()?new Promise((function(e,n){if(a.a.appVersion()>=8.0803)o.a.exec("scanCode",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{window.getScancodeContent=function(t){if(t){e({result:t})}else{var r=new Error;r.data={status:"11",msg:"用户主动取消了或者没有扫描到结果"},n(r)}};var t={type:"openScanCode"};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):a.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))}})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持scanCode方法"},n(t)}))},decodeQRCode:function(e){if(a.a.isInApp()&&a.a.appVersion()>8.0901){var n=e.base64Data;n&&(n=n.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,",""));var t={url:e.url,base64Data:n};return new Promise((function(e,n){o.a.exec("decodeQRCode",t,(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}))}return new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持decodeQRCode方法"},n(t)}))},setClipboardData:function(e,n){if(a.a.isInApp())if(a.a.appVersion()>=8.0803){var t="yes";!0===n&&(t="no"),o.a.execSync("setClipboardData",{data:e,toast:t})}else console.warn("此版本不支持调用setClipboardData方法。");else console.warn("端外不支持调用setClipboardData方法。")},getClipboardData:M,getClipboardDataAsync:function(e){return new Promise((function(n,t){if(a.a.isInApp())if(a.a.appVersion()>=8.0803){if(a.a.isAndroid()||a.a.isIOS()){var r=M(e);n(r)}else if(a.a.isHarmony()){var i="yes";!0===e&&(i="no"),o.a.exec("getClipboardDataAsync",{clear:i},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}}else console.warn("此版本不支持调用getClipboardData方法。");else console.warn("端外不支持调用getClipboardData方法。")}))},request:s.a,FileDownloadTask:O,createFileDownloadTask:function(e){if(console.log("createFileDownloadTask-创建返回下载任务",e),a.a.isInApp()&&a.a.appVersion()>=8.09){var n=o.a.execSync("downloadFile",Object(r.a)({type:"createFileDownloadTask"},e)),t=new O(n);return"RUNNING"==t.taskStatus&&t.startDownload(),t}var i=new Error;return i.data="不支持调用createFileDownloadTask方法。",i},createFileDownloadTaskAsync:function(e){if(console.log("createFileDownloadTaskAsync-创建返回下载任务",e),a.a.isInApp()&&a.a.appVersion()>=8.09)return new Promise((function(n,t){o.a.exec("downloadFile",Object(r.a)({type:"createFileDownloadTask"},e),(function(e){var t=new O(e);"RUNNING"==t.taskStatus&&t.startDownload(),n(t)}),(function(e){var n=new Error;n.data=e,t(n)}))}));var n=new Error;return n.data="当前环境不支持createFileDownloadTask方法",n},FileUploadTask:k,uploadFile:function(e){if(console.log("uploadFile上传文件",e),a.a.isInApp()&&a.a.appVersion()>=8.09){var n=Number((new Date).getTime()+""+Math.round(100*Math.random())+Math.round(100*Math.random())),t=new k(n),i=Object(r.a)({uploadId:n},e);return o.a.exec("uploadFile",Object(r.a)({type:"startUpload"},i),(function(e){var n=e.data;"progressUpdate"==e.action?t.onProgressUpdate(n.totalBytesSent,n.totalBytesExpectedToSend,n.speed):"complete"==e.action?(console.log("uploadFile回调监听",JSON.stringify(e)),t.onComplete(n.httpStatusCode,n.response)):"cancel"==e.action&&(console.log("uploadFile回调监听",JSON.stringify(e)),t.onCancel())}),(function(e){var n=new Error;n.data=e,t.onError(n)}),!0),t}var s=new Error;return s.data="不支持调用uploadFile方法。",s},chooseFile:function(e){return a.a.isInApp()&&a.a.appVersion()>=8.09?new Promise((function(n,t){o.a.exec("chooseFile",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用chooseFile方法。",n(t)}))},chooseWechatFile:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.05?new Promise((function(n,t){i({scope:"scope.wechatfile",action:"chooseWechatFile"}).then((function(){console.log("调用chooseWechatFile，自动请求scope.wechatfile授权，验证通过"),o.a.exec("chooseWechatFile",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})).catch((function(e){console.log("调用chooseWechatFile，自动请求scope.wechatfile授权，验证拒绝"),t(e)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用chooseWechatFile方法。",n(t)}))},getClientInfo:function(){return a.a.isInApp()?new Promise((function(e,n){if(a.a.appVersion()>=7.06)o.a.exec("getClientInfo",{},(function(n){n.currentPhoneNumber||(n.currentPhoneNumber=""),e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else if(u.push(e),l.push(n),window.setClientInfo=function(e){var n=JSON.parse(e);"0"==n.currentPhoneNumber&&(n.currentPhoneNumber=""),n.appVersion=n.clientVersion,n.ywCode=n.yw_code,n.provinceCode_1=n.locateProvinceCode,n.cityCode_1=n.locateCityCode,n.statusBarHeight=n.statusBar,a.a.isIOS()?(n.iosPushToken=n.deviceId,n.devicedId=n.deviceCode,n.isLogin="1"==n.isLoginOn,n.deviceBrand="iphone"==n.deviceBrand?"apple":deviceBrand):a.a.isAndroid()&&(n.devicedId=n.imei,n.huaweiPushToken=n.platformToken,n.isLogin=""!=n.currentPhoneNumber);var t=n.cookies;if(t instanceof Array)for(var r=0;r<t.length;r++){var o=t[r];if(a.a.isIOS()){if("ecs_token"==o.Name){n.ecs_token=o.Value;break}}else if(a.a.isAndroid()){if("ecs_token"==o.name){n.ecs_token=o.value;break}}}n?u.forEach((function(e){e(n)})):l.forEach((function(e){e(null)})),u=[],l=[]},a.a.isIOS()){a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify({type:"getClientInfo"})))}else a.a.isAndroid()&&window.setClientInfo(window.js_invoke.getClientInfoByJS())})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持getClientInfo方法"},n(t)}))},getClientInfoSync:f,getSystemInfo:function(){return a.a.isInApp()&&a.a.appVersion()>=8.0803?o.a.execSync("getSystemInfo"):{status:"10",msg:"当前环境不支持getSystemInfo方法"}},getSystemInfoAsync:function(){return new Promise((function(e,n){if(a.a.isInApp()&&a.a.appVersion()>=8.0803)if(a.a.isAndroid()||a.a.isIOS()){var t=o.a.execSync("getSystemInfo");e(t)}else if(a.a.isHarmony())o.a.exec("getSystemInfo",{},(function(n){e(n)}),(function(e){n(e)}));else{n({status:"10",msg:"当前环境不支持getSystemInfo方法"})}else{n({status:"10",msg:"当前环境不支持getSystemInfo方法"})}}))},getUserInfo:function(){return new Promise((function(e,n){if(c())i({scope:"scope.unicomaccount",action:"getUserInfo"}).then((function(){if(console.log("调用getUserInfo，自动请求scope.unicomaccount授权，验证通过"),a.a.isInApp()&&a.a.appVersion()>=8.0803){var t=o.a.execSync("getUserInfo");e(t)}else{var r=new Error;r.data={status:"10",msg:"当前环境不支持getUserInfo方法"},n(r)}})).catch((function(e){console.log("调用getUserInfo，自动请求scope.unicomaccount授权，验证拒绝"),n(e)}));else{var t=new Error;t.data={status:"11",msg:"用户未登录，请先登录"},n(t)}}))},isLoginSync:function(){return!a.a.isInApp()||(a.a.appVersion()>=8?o.a.execSync("isLogin"):!!a.a.getCookieFromDocument("ecs_token"))},isLogin:c,loginByClient:function(e){if(a.a.isInApp())if(e&&"y"===e.isNewChannel&&a.a.appVersion()>=10.08)o.a.exec("login",e);else{var n={type:"login",msg:e&&e.msg?e.msg:"",url:e&&e.url?e.url:""};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n))):a.a.isAndroid()?window.js_invoke.interact(JSON.stringify(n)):a.a.isHarmony()&&o.a.exec("login",e)}else console.warn("端外不支持调用loginByClient方法。")},getTicket:function(e){var n={url:"https://m.client.10010.com/edop_ng/getTicketByNative",method:"GET",requestDataType:"FORM",data:{token:e.ecsToken,appId:e.appId}};return Object(s.a)(n)},getNetAccessCode:function(){return a.a.isInApp()&&a.a.appVersion()>=8.09?new Promise((function(e,n){o.a.exec("getNetAccessCode",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="不支持调用getNetAccessCode方法。",n(t)}))},getNetAccessCodeToH5:function(e){return a.a.isInApp()&&a.a.appVersion()>=8.09?new Promise((function(n,t){if(e&&e.businessId&&p&&p.getNetAccessCode){var r,a;if(p.getNetAccessCode.forEach((function(n){n.businessId==e.businessId&&(r=n.businessId,a=n.businessName)})),r&&a)console.log("调用getNetAccessCode需要渠道中心授权，当前授权businessId="+r+" 所属业务为"+a+"，请确认是否违规调用。"),o.a.exec("getNetAccessCode",{},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)})),function(e,n){var t={url:"https://smartad.10010.com/msupport/count/businessLogRecords.htm",method:"POST",requestDataType:"FORM",data:{transId:"S2ndpage1094",actCode:"getNetAccessCodeToH5",menuId:e,titleName:n,remark1:window.location.href}};Object(s.a)(t)}(r,a);else{var i=new Error;i.data="调用getNetAccessCode需要渠道中心授权，请先申请授权",t(i)}}else{var c=new Error;c.data="调用getNetAccessCode需要渠道中心授权，请先申请授权",t(c)}})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用getNetAccessCodeToH5方法。",n(t)}))},navigateTo:h,homeNavigator:function(e){if(a.a.appVersion()>=11.07)return new Promise((function(n,t){o.a.exec("homeNavigator",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}));console.warn("非11.7及以上版本不支持调用homeNavigator方法。")},homeNavigatorSync:function(e){if(a.a.appVersion()>=11.07)return o.a.execSync("homeNavigator",e);console.warn("非11.7及以上版本不支持调用homeNavigator方法。")},navigateGoBack:function(e){return a.a.isInApp()?d(e||{},"navigateGoBack"):new Promise((function(n,t){var r=-1;e&&e.num&&((r=parseInt(e.num))>=0&&(r=-1),Math.abs(r)>=history.length&&(r=-(history.length-1))),r<0&&window.history.go(r)}))},navigateClose:g,navigateCloseOne:function(){if(a.a.appVersion()>=11.06&&a.a.isIOS())return o.a.exec("navigateCloseOne");console.warn("非iOS11.6及以上版本不支持调用NavigationColoseOne方法。")},navigateCloseOneSync:function(){if(a.a.appVersion()>=11.06&&a.a.isIOS())return o.a.execSync("navigateCloseOne");console.warn("非iOS11.6及以上版本不支持调用NavigationColoseOne方法。")},navigateParams:function(){return d({},"navigateParams")},navigateBackParams:function(){return d({},"navigateBackParams")},navigationBarHidden:function(){return!!(a.a.isInApp()&&a.a.appVersion()>=8.0704)&&(window.sessionStorage.setItem("navigationBarHidden_isFullScreeen","fullscreen"),o.a.execSync("navigationBarHidden"))},setNavigationBarTitle:function(e){if(e&&(document.title=e,a.a.isInApp()))if(a.a.isIOS()){var n={type:"handJSTitle",msg:e};a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(n)))}else if(a.a.isAndroid())window.js_invoke.handleJSTitle(e);else if(a.a.isHarmony()){var t={title:e};o.a.execSync("setNavigationBarTitle",t)}},setNavigationBarColor:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.0001?new Promise((function(n,t){o.a.exec("setNavigationBarColor",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){e({})}))},redirectTo:function(e){g(),a.a.isHarmony()?setTimeout((function(){h(e)}),100):h(e)},setStorage:function(e,n){var t={key:e,value:n};return a.a.isInApp()&&a.a.appVersion()>7.06?m(t,"setStorage"):new Promise((function(n){window.localStorage.setItem(e,JSON.stringify(t)),n({status:"success",key:e,msg:""})}))},setStorageSync:function(e,n){var t={key:e,value:n};return a.a.isInApp()&&a.a.appVersion()>7.06?o.a.execSync("setStorage",t):(window.localStorage.setItem(e,JSON.stringify(t)),{status:"success",key:e,msg:""})},setCrossDomainStorageSync:function(e,n){var t={key:e,value:n};return a.a.isInApp()&&a.a.appVersion()>=9.0201?o.a.execSync("setCrossDomainStorageSync",t):(window.localStorage.setItem(e,JSON.stringify(t)),{status:"fail",key:e,msg:"请在联通App9.2.1及以上版本内调用"})},getStorage:function(e){return a.a.isInApp()&&a.a.appVersion()>7.06?m({key:e},"getStorage"):new Promise((function(n){var t=window.localStorage.getItem(e),r=JSON.parse(t);n(r?r.value:"")}))},getStorageSync:function(e){if(a.a.isInApp()&&a.a.appVersion()>7.06){var n={key:e};return o.a.execSync("getStorage",n)}var t=window.localStorage.getItem(e),r=JSON.parse(t);return r&&r.value?r.value:""},getCrossDomainStorageSync:function(e){if(a.a.isInApp()&&a.a.appVersion()>=9.0201){var n={key:e};return o.a.execSync("getCrossDomainStorageSync",n)}var t=window.localStorage.getItem(e),r=JSON.parse(t);return r&&r.value?r.value:""},getCrossDomainStorageAsync:function(e){return new Promise((function(n,t){if(!(a.a.isInApp()&&a.a.appVersion()>=9.0201)){var r=window.localStorage.getItem(e),i=JSON.parse(r);return i&&i.value?i.value:""}if(a.a.isAndroid()||a.a.isIOS()){var s={key:e},c=o.a.execSync("getCrossDomainStorageSync",s);n(c)}else{if(!a.a.isHarmony()){var u=window.localStorage.getItem(e),l=JSON.parse(u);return l&&l.value?l.value:""}var f={key:e};o.a.exec("getCrossDomainStorageAsync",f,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}}))},deleteStorage:function(e){return a.a.isInApp()&&a.a.appVersion()>7.06?m({key:e},"deleteStorage"):new Promise((function(n){window.localStorage.removeItem(e),n({status:"success",key:e,msg:""})}))},deleteStorageSync:function(e){if(a.a.isInApp()&&a.a.appVersion()>7.06){var n={key:e};return o.a.execSync("deleteStorage",n)}return window.localStorage.removeItem(e),{status:"success",key:e,msg:""}},deleteCrossDomainStorageSync:function(e){if(a.a.isInApp()&&a.a.appVersion()>=9.0201){var n={key:e};return o.a.execSync("deleteCrossDomainStorageSync",n)}return window.localStorage.removeItem(e),{status:"success",key:e,msg:""}},clearStorage:function(){if(a.a.isInApp()&&a.a.appVersion()>=8.0803){return m({},"clearStorage")}return new Promise((function(e,n){var t=new Error;t.data="不支持clearStorage方法。",n(t)}))},clearStorageSync:function(){if(a.a.isInApp()&&a.a.appVersion()>=8.0803){return o.a.execSync("clearStorage",{})}return{status:"fail",msg:"不支持clearStorageSync"}},getStorageInfo:function(){if(a.a.isInApp()&&a.a.appVersion()>=8.0803){return m({},"getStorageInfo")}return new Promise((function(e,n){var t=new Error;t.data="不支持getStorageInfo方法。",n(t)}))},getStorageInfoSync:function(){if(a.a.isInApp()&&a.a.appVersion()>=8.0803){return o.a.execSync("getStorageInfo",{})}return{status:"fail",msg:"不支持getStorageInfoSync"}},chooseContact:function(){return a.a.isInApp()?new Promise((function(e,n){if(a.a.appVersion()>8.0705)i({scope:"scope.contacts",action:"chooseContact"}).then((function(){console.log("调用chooseContact，自动请求scope.contacts授权，验证通过"),o.a.exec("chooseContact",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})).catch((function(e){console.log("调用chooseContact，自动请求scope.contacts授权，验证拒绝"),n(e)}));else{window.clientSetContactPhoneNum=function(t){if(t){var r=JSON.parse(t);if(r&&r.telinfo&&Array.isArray(r.telinfo)&&r.telinfo.length>0){var a=[],o={phoneNumber:"",displayName:"",phoneNumberList:a};return r.telinfo.forEach((function(e){var n=e.name,t=e.phone;o.displayName=n,a.push(t)})),o.phoneNumber=a[0],o.phoneNumberList=a,void e(o)}}var i=new Error;i.data={status:"10",msg:"没有联系人信息，请升级APP新版本"},n(i)};var t={type:"openContact"};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(t))):a.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(t))}})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持chooseContact方法"},n(t)}))},searchContacts:function(e){return new Promise((function(n,t){i({scope:"scope.contacts",action:"searchContacts"}).then((function(){if(console.log("调用searchContacts，自动请求scope.contacts授权，验证通过"),a.a.isIOS()&&9.05==a.a.appVersion()&&a.a.isEdop())G.checkSystemPermission("contacts").then((function(){window.setAllContent=function(e){var t=[];e.forEach((function(e){e.letter.forEach((function(e){t.push(e)}))}));var r=[];t.forEach((function(e){e.phoneList instanceof Array&&e.phoneList.forEach((function(n){r.push({phoneNumber:n,name:e.name})}))})),n(r)};a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify({type:"getAllContent"})))})).catch((function(e){var n=new Error;n.data=e,t(n)}));else if(a.a.isInApp()&&a.a.appVersion()>8.0705){var r={phoneNumber:e||""};o.a.exec("searchContacts",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持searchContacts方法"},t(i)}})).catch((function(e){console.log("调用searchContacts，自动请求scope.contacts授权，验证拒绝"),t(e)}))}))},searchCallLog:function(e){return new Promise((function(n,t){i({scope:"scope.calllog",action:"searchCallLog"}).then((function(){if(console.log("调用searchCallLog，自动请求scope.calllog授权，验证通过"),a.a.isInApp()&&a.a.appVersion()>8.0904){var r={callNumber:e||""};o.a.exec("searchCallLog",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持searchCallLog方法"},t(i)}})).catch((function(e){console.log("调用searchCallLog，自动请求scope.calllog授权，验证拒绝"),t(e)}))}))},callPhone:function(e){return a.a.isInApp()&&a.a.appVersion()>8.0904?new Promise((function(n,t){o.a.exec("callPhone",e,(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持callPhone方法"},n(t)}))},sendSMS:function(e){return a.a.isInApp()&&a.a.appVersion()>8.0904?new Promise((function(n,t){o.a.exec("sendSMS",e,(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持sendSMS方法"},n(t)}))},getLocationInfo:function(e){return new Promise((function(n,t){i({scope:"scope.location",action:"getLocationInfo"}).then((function(){if(console.log("调用getLocationInfo，自动请求scope.location授权，验证通过"),a.a.isInApp())if(a.a.appVersion()>=7.06)e||(e={}),o.a.exec("getLocationInfo",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}));else{v.push(n),w.push(t);var r={callbackFromNative:function(e,n){if("lbsData"==e){var t=JSON.parse(n);if(t&&1==t.status&&t.latitude&&t.longitude)t.provinceName=t.province,t.cityName=t.city,v.forEach((function(e){e(t)}));else{var r={};t&&12==t.status?(r.status=12,r.msg="用户没有开启APP的定位权限"):(r.status=10,r.msg="程序异常");var a=new Error;a.data=r,w.forEach((function(e){e(a)}))}}v=[],w=[]}};if(window.bridge=r,a.a.isIOS()){a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify({type:"getGeoLocation"})))}else a.a.isAndroid()&&window.js_invoke.getGeoLocation()}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持getLocationInfo方法"},t(i)}})).catch((function(e){console.log("调用getLocationInfo，自动请求scope.location授权，验证拒绝"),t(e)}))}))},onAppShow:function(e){o.a.addEventListener("onPageShow",e)},onAppHide:function(e){o.a.addEventListener("onPageHidden",e)},onPageHidden:function(e){o.a.addEventListener("onPageHidden",e)},onPageShow:function(e){o.a.addEventListener("onPageShow",e)},onPageScrollToEnd:function(e,n){a.a.isInApp()&&!n?o.a.addEventListener("pageScrollToEnd",e):window.addEventListener("scroll",(function(){Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)-function(){var e=0;document.documentElement&&document.documentElement.scrollTop?e=document.documentElement.scrollTop:document.body&&(e=document.body.scrollTop);return e}()-function(){var e=0;e=document.body.clientHeight&&document.documentElement.clientHeight?Math.min(document.body.clientHeight,document.documentElement.clientHeight):Math.max(document.body.clientHeight,document.documentElement.clientHeight);return e}()<20&&e()}))},openSystemSetting:function(e){"string"!=typeof e&&(e=""),o.a.exec("openSystemSetting",{type:e})},webView:E,getAllNavMenu:function(){return a.a.isInApp()?new Promise((function(e,n){o.a.exec("clientNavMenu",{type:"getAllNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getAllNavMenu方法。",n(t)}))},getChangYongNavMenu:function(){return a.a.isInApp()?new Promise((function(e,n){o.a.exec("clientNavMenu",{type:"getChangYongNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getChangYongNavMenu方法。",n(t)}))},getHomeCustomNavMenu:function(){return a.a.isInApp()?new Promise((function(e,n){o.a.exec("clientNavMenu",{type:"getHomeCustomNavMenu"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getHomeCustomNavMenu方法。",n(t)}))},updateHomeCustomNavMenu:function(e){return a.a.isInApp()?new Promise((function(n,t){o.a.exec("clientNavMenu",{type:"updateHomeCustomNavMenu",value:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=JSON.parse(e),t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持updateHomeCustomNavMenu方法。",n(t)}))},getLanguageJson:function(){return a.a.isInApp()?new Promise((function(e,n){o.a.exec("clientNavMenu",{type:"getLanguageJson"},(function(n){e(n)}),(function(e){var t=new Error;t.data=JSON.parse(e),n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持getLanguageJson方法。",n(t)}))},checkAppInstalled:function(e){return!!(a.a.isInApp()&&a.a.appVersion()>=8.0904)&&o.a.execSync("checkAppInstalled",e)},isFullScreen:function(){var e=window.sessionStorage.getItem("navigationBarHidden_isFullScreeen");if(a.a.isEdop()||window.location.href.indexOf("webViewNavIsHidden")>-1||e&&"fullscreen"==e)return!0;if(a.a.isInApp()){if(a.a.appVersion()>=9.0002)return o.a.execSync("isFullScreen",{});var n=f();return n.screenHeight==n.windowHeight}return!0},showMsg:function(e){if(a.a.isInApp())return new Promise((function(n,t){o.a.exec("alert",e,(function(){n()}),(function(){t()}))}));alert("端外模拟弹窗："+e.msg)},toast:function(e,n){n||(n=2e3),e||(e="");var t={msg:e,time:n};if(a.a.isInApp())return new Promise((function(n,r){e?o.a.exec("toast",t,(function(e){n(e)}),(function(e){var n=new Error;n.data=JSON.parse(e),r(n)})):r()}));alert("端外模拟Toast："+e)},sentBodyHeightToNative:function(){window.sentBodyHeightToNativeOn=!0;var e=document.getElementsByTagName("html")[0];new MutationObserver(_).observe(e,{attributes:!0,childList:!0,subtree:!0}),_()},getGameDownloadTask:function(e){if(a.a.isInApp()){var n=o.a.execSync("getGameDownloadTask",e),t=new C(n);return"RUNNING"==t.taskStatus&&t.startDownload(),t}var r=new Error;return r.data="端外不支持游戏分发接口调用",r},GameDownloadTask:C,getNetworkType:function(){return a.a.isInApp()?I(o.a.execSync("getNetworkType")):{networkType:"wifi",cellularType:"",mnc:-1,carrier:""}},getNetworkTypeAsync:function(){return new Promise((function(e,n){if(a.a.isInApp()&&(a.a.isAndroid()||a.a.isIOS())){var t=I(o.a.execSync("getNetworkType"));e(t)}else a.a.isInApp()&&a.a.isHarmony()?o.a.exec("getNetworkType",{},(function(n){var t=I(n);e(t)}),(function(e){var t=new Error;t.data=e,n(t)})):e({networkType:"wifi",cellularType:"",mnc:-1,carrier:""})}))},ping:function(e,n,t){if(a.a.isInApp())if(a.a.appVersion()>=8.09)o.a.exec("ping",Object(r.a)({},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}),!0);else{var i=new Error;i.data={status:"30",msg:"8.9以下版本不支持ping方法"},t(i)}else n({code:"10",sequenceNumber:"1",consumeTime:"9.35",msg:"ping请求成功"})},onNetworkStatusChanged:function(e){return o.a.addEventListener("onNetworkStatusChanged",e)},offNetworkStatusChanged:function(e){o.a.removeEventListener("onNetworkStatusChanged",e)},getConnectedWifi:function(){return new Promise((function(e,n){if(a.a.isInApp())if(a.a.appVersion()>9.0001)o.a.exec("getConnectedWifi",{},(function(n){n.stasus&&(n.status=n.stasus),e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{var t=new Error;t.data={status:"30",msg:"9.0.2以下版本不支持getConnectedWifi方法"},n(t)}else e({SSID:"",BSSID:""})}))},getRSRP:function(){return new Promise((function(e,n){if(a.a.isInApp())if(a.a.isAndroid()&&a.a.appVersion()>=9.02)o.a.exec("getRSRP",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}));else{var t=new Error;t.data={status:"30",msg:"仅Android9.2及以上版本支持该方法"},n(t)}else{var r=new Error;r.data={status:"30",msg:"仅Android9.2及以上版本支持该方法"},n(r)}}))},checkSystemPermission:function(e){return a.a.isInApp()?new Promise((function(n,t){o.a.exec("systemPermission",{type:"checkSystemPermission",permission:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外暂不支持checkSystemPermission方法。",n(t)}))},accessibilityStatus:x,accessibilitySay:function(e){a.a.isInApp()&&a.a.appVersion()>=10.06?x()?o.a.exec("UIAccessibilityManager",{type:"sayText",msg:e}):console.log("旁白模式（无障碍模式）没有打开，不支持UIAccessibilityManager：sayText方法"):console.log("端外暂不支持UIAccessibilityManager方法")},accessibilityStatusListen:function(e){return x(),o.a.addEventListener("UIAccessibilityManagerStatusChange",e)},accessibilityStatusListenStop:function(e){o.a.removeEventListener("UIAccessibilityManagerStatusChange",e)},chooseImage:function(e){return a.a.isInApp()?new Promise((function(n,t){if(a.a.appVersion()>8.0703)i({scope:"scope.album",action:"chooseImage"}).then((function(){console.log("调用chooseImage，自动请求scope.album授权，验证通过"),o.a.exec("chooseImage",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})).catch((function(e){console.log("调用chooseImage，自动请求scope.album授权，验证拒绝"),t(e)}));else{T.push(n),L.push(t),window.callbackWithImage=function(e){if(e){var n={imageType:"jpg",imageData:e};T.forEach((function(e){e(n)}))}else{var t=new Error;t.data={status:"10",msg:"程序异常"},L.forEach((function(e){e(t)}))}T=[],L=[]};var r="0";e&&"camera"==e.sourceType?r="1":e&&"album"==e.sourceType&&(r="2");var s={type:"takePhotoOrCamera",msg:r};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):a.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用chooseImage方法。",n(t)}))},saveImageToPhotosAlbum:function(e){return new Promise((function(n,t){var r=(new Date).getTime();if(r-D>=5e3){D=r;var i=e.base64Data;if(i=i.replace("data:image/jpeg;base64,","").replace("data:image/jpg;base64,","").replace("data:image/gif;base64,","").replace("data:image/png;base64,",""),a.a.isInApp()&&a.a.appVersion()>=11.02)if(e&&e.base64Data){var s={base64Data:i};o.a.exec("saveImageToPhotosAlbum",s,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else console.warn("saveImageToPhotosAlbum按文档传入指定参数");else if(a.a.isInApp()){if(e&&e.base64Data){s={type:"saveBase64Image",msg:i};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(s))):a.a.isAndroid()&&window.js_invoke.interact(JSON.stringify(s))}}else console.warn("端外不支持调用saveImageToPhotosAlbum方法。")}else console.warn("5秒内不允许重复使用该方法")}))},handSignature:function(e){return a.a.isInApp()?new Promise((function(n,t){window.setSignPicture=function(e){if(e&&"0"==e.code){var r=e.data;n(r)}else{var a=new Error;a.data="没有正常生成手写签名数据",t(a)}};var r={type:"getSignPicture",msg:e&&e.hintText?e.hintText:"",needYaSuo:!0,width:"800",height:"100"};a.a.isIOS()?a.a.execIOSJSBridge(encodeURI("clientAction="+JSON.stringify(r))):a.a.isAndroid()?window.js_invoke.interact(JSON.stringify(r)):a.a.isHarmony()&&MsJSBridge.exec("getSignPicture",r,(function(e){if(e&&"0"==e.code){var r=e.data;n(r)}else{var a=new Error;a.data="没有正常生成手写签名数据",t(a)}}),(function(e){var n=new Error;n.data="没有正常生成手写签名数据",t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用handSignature方法。",n(t)}))},exec:function(e,n){if(a.a.isInApp())return new Promise((function(t,r){o.a.exec(e,n,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}),!1)}));alert("端外不支持方法："+e)},execSync:function(e,n){return a.a.isInApp()?o.a.execSync(e,n):(alert("端外不支持方法："+e),null)},execKeep:function(e,n){var t=new U;return o.a.exec(e,n,(function(e,n){t.successData=e||"",t.resolve&&t.resolve(e),0==n&&t.endBlock&&t.endBlock()}),(function(e,n){var r=new Error;r.data=e,t.failData=r,t.reject&&t.reject(r),0==n&&t.endBlock&&t.endBlock()}),!0),t},execKeepSendMsg:function(e,n){o.a.exec(e,n,null,null,!0)},pageFontModel:function(){if(a.a.appVersion()>=8.03){var e=o.a.execSync("fontSizeModel");if("string"==typeof e)return e}return"0"},getLanguageInfo:function(){return a.a.isInApp()&&a.a.appVersion()>=8.0702?o.a.execSync("getLanguageInfo"):"chinese"},addEventListener:function(e,n){o.a.addEventListener(e,n)},allowUserCaptureScreen:function(e){return a.a.isInApp()&&a.a.appVersion()>=8.0804?o.a.execSync("screenshots",{type:"setScreenshots",params:{allowAcreenshots:e?"0":"1"}}):"不支持调用allowUserCaptureScreen方法。"},onUserCaptureScreen:function(e){return o.a.addEventListener("onUserCaptureScreen",e)},offUserCaptureScreen:function(e){o.a.removeEventListener("onUserCaptureScreen",e)},vibrateShort:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"heavy";return a.a.isInApp()?new Promise((function(n,t){if(a.a.appVersion()>=8.0901)o.a.exec("vibrateShort",{type:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}));else{var r=new Error;r.data="不支持调用vibrateShort方法。",t(r)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用vibrateShort方法。",n(t)}))},addPhoneCalendar:function(e){return a.a.isInApp()&&a.a.appVersion()>8.0904?new Promise((function(n,t){var r={};e&&(r=e),o.a.exec("addPhoneCalendar",r,(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持addPhoneCalendar方法"},n(t)}))},faceDetect:function(e){return new Promise((function(n,t){i({scope:"scope.faceDetect",action:"FacePlusPlus"}).then((function(){if(console.log("调用faceDetect，自动请求scope.faceDetect授权，验证通过"),a.a.isInApp)if(a.a.appVersion()>=8.0704)o.a.exec("FacePlusPlus",{type:"customCheckLive",params:e},(function(e){a.a.isAndroid()&&e&&e.resultCode&&"0"==e.resultCode&&e.resultInfo&&"string"==typeof e.resultInfo&&(e.resultInfo=JSON.parse(e.resultInfo)),n(e)}),(function(e){a.a.isAndroid()&&e&&e.resultCode&&"0"==e.resultCode&&e.resultInfo&&"string"==typeof e.resultInfo&&(e.resultInfo=JSON.parse(e.resultInfo)),n(e)}));else{var r=new Error;r.data={status:"10",msg:"当前环境不支持faceDetect方法"},t(r)}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持faceDetect方法"},t(i)}})).catch((function(e){console.log("调用faceDetect，自动请求scope.faceDetect授权，验证拒绝"),t(e)}))}))},faceV3Detect:function(e){return new Promise((function(n,t){i({scope:"scope.faceDetect",action:"faceV3Detect"}).then((function(){if(console.log("调用faceV3Detect，自动请求scope.faceDetect授权，验证通过"),a.a.isInApp)if(a.a.appVersion()>=10.06)o.a.exec("faceV3Detect",e,(function(e){n(e)}),(function(e){n(e)}));else{var r=new Error;r.data={status:"10",msg:"当前环境不支持faceV3Detect方法"},t(r)}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持faceV3Detect方法"},t(i)}})).catch((function(e){console.log("调用faceV3Detect，自动请求scope.faceDetect授权，验证拒绝"),t(e)}))}))},getUnicomCodeByBaiduCode:function(e,n){return new Promise((function(t,r){var a=Boolean(n);console.log("是否测试环境",a);var o={url:a?"http://ecstest2018.10010.com/mobileserviceimportant_test/customerService/getUnicomCodeByBaiduCode":"https://m.client.10010.com/mobileserviceimportant/customerService/getUnicomCodeByBaiduCode",method:"POST",requestDataType:"FORM",data:{baiduCodeCoun:e}};Object(s.a)(o).then((function(e){var n=JSON.parse(e);t(n)})).catch((function(e){r(e)}))}))},openNFC:function(){return a.a.isInApp()?new Promise((function(e,n){if(a.a.appVersion()>=9.01&&a.a.isAndroid())o.a.exec("nfcOpen",{},(function(){e()}),(function(e){var t=new Error;t.data=e,n(t)}));else{var t=new Error;t.data="不支持调用openNFC方法。",n(t)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用openNFC方法。",n(t)}))},openAndTransmitSim:j,getIccid:function(e,n){console.log("getIccid第0步");var t={apduList:[{apdu:"00A4000C023F00",sw:"9000"},{apdu:"00A4000C022FE2",sw:"9000"},{apdu:"00B000000A",sw:"9000"}],aid:"A0000000871002FF86FFFF89FFFFFFFF"};if(console.log("getIccid第1步"),a.a.appVersion()>=9.02&&a.a.isInApp())console.log("getIccid第2步"),o.a.exec("OMApi",Object(r.a)({type:"getSimAllInfoOnExec"},t),(function(r){if(console.log("getIccid第3步"),t.apduList.length+1==r.length&&"0000"==r[r.length-1].status){console.log("getIccid第4步");var a=r[r.length-1].resRapdu;if(a.length%2==0){for(var o="",i=0;i<a.length/2;i++){var s=a.substr(o.length,2).split("");o+=s.splice(-1).concat(s).join("")}console.log("getIccid第5步"),e({iccid:o})}else console.log("getIccid第6步"),n({iccid:a,status:"13",msg:"不支持基数iccid换位"})}else console.log("getIccid第7步"),1==r.length?n({status:r[0].status,msg:r[0].msg}):n({status:"12",msg:r[r.length-1].msg,position:r.length-1})}),(function(e){console.log("getIccid第8步");var t=new Error;t.data=e,n(t)}),!1);else{console.log("getIccid第9步");var i=new Error;i.data={status:"10",msg:"当前环境不支持getIccid方法"},n(i)}},getBanlaceAndCardNumber:function(e,n,t){var i={apduList:[{apdu:"00B095001E",sw:"9000"},{apdu:"805C000204",sw:"9000"}],aid:e};if(a.a.appVersion()>=9.02&&a.a.isInApp())o.a.exec("OMApi",Object(r.a)({type:"getSimAllInfoOnExec"},i),(function(e){if(console.log("获取卡的文件信息1:",e),3==e.length&&"0000"==e[2].status){var r=e[1].resRapdu,a=e[2].resRapdu,o=r.substring(21,40),i=r.substring(40,48),s=r.substring(48,56),c=parseInt(a.substring(0,16),16);n({cardNumber:o,startDate:i,endDate:s,walletBalance:c})}else 1==e.length?t({status:e[0].status,msg:e[0].msg}):t({status:"12",msg:e[e.length-1].msg,position:e.length-1})}),(function(e){var n=new Error;n.data=e,t(n)}),!0);else{var s=new Error;s.data={status:"10",msg:"当前环境不支持getBanlaceAndCardNumber方法"},t(s)}},getTransactionRecords:function(e,n,t){var i={apduList:[{apdu:"00B201C417",sw:"9000"},{apdu:"00B202C417",sw:"9000"},{apdu:"00B203C417",sw:"9000"},{apdu:"00B204C417",sw:"9000"},{apdu:"00B205C417",sw:"9000"},{apdu:"00B206C417",sw:"9000"},{apdu:"00B207C417",sw:"9000"},{apdu:"00B208C417",sw:"9000"},{apdu:"00B209C417",sw:"9000"},{apdu:"00B20AC417",sw:"9000"}],aid:e};if(a.a.appVersion()>=9.02&&a.a.isInApp())o.a.exec("OMApi",Object(r.a)({type:"getSimAllInfoOnExec"},i),(function(e){console.log("交易记录详情",e),console.log("交易记录详情1",e.length),console.log("交易记录详情2",i.apduList.length),console.log("交易记录详情3",i.apduList.length),i.apduList.length+1==e.length&&"0000"==e[e.length-1].status?n({tradeList:F(e)}):1==e.length?t({status:e[0].status,msg:e[0].msg}):t({status:"12",msg:e[e.length-1].msg,position:e.length-1})}),(function(e){var n=new Error;n.data=e,t(n)}),!0);else{var s=new Error;s.data={status:"10",msg:"当前环境不支持getBanlaceAndCardNumber方法"},t(s)}},getDefaultCardInfo:function(e,n){N=[],J("A00000015143525300").then((function(){W({apdu:"80F240000F4F085943542E555345525C039F704F00",sw:"9000"}).then((function(t){if(console.log("发送80F240000F4F085943542E555345525C039F704F00指令",t),"0000"==t.status)if(t.resRapdu.includes("9F700207")){var r=t.resRapdu.indexOf("9F700207");if("01"==t.resRapdu.substring(r+8,r+8+2)){console.log("羊城通ok");var a=t.resRapdu.indexOf("4F08"),o=parseInt(t.resRapdu.substring(a+2,a+4),16),i=t.resRapdu.substring(a+4,a+4+2*o);e({cardAid:i})}else console.log("羊城通00"),V(e,n)}else console.log("羊城通不包含9F700207"),V(e,n);else console.log("80F240000F4F085943542E555345525C039F704F00指令失败"),V(e,n)})).catch((function(e){var t=new Error;t.data=e,n(t)}))})).catch((function(e){var t=new Error;t.data=e,n(t)}))},setDefaultCardInfo:function(e,n,t,i){if(a.a.isInApp()&&a.a.appVersion()>=9.02){var s=parseInt(e.length/2).toString(16).toLocaleUpperCase();1==s.length&&(s="0"+s);var c="4F"+s+e,u=parseInt(c.length/2).toString(16).toLocaleUpperCase();1==u.length&&(u="0"+u);var l=[{apdu:"80F00101"+u+c,sw:n}];o.a.exec("OMApi",Object(r.a)({apduList:l,type:"getSimAllInfoOnExec"},{aid:"A00000015143525300"}),(function(e){2==e.length&&e[1].resSw==n?t():i({data:e,status:"13",msg:"设置默认卡失败"})}),(function(e){var n=new Error;n.data=e,i(n)}),!0)}else{var f=new Error;f.data={status:"10",msg:"当前环境不支持setDefaultCardInfo方法"},i(f)}},cancelDefaultCard:function(e,n,t,i){if(a.a.isInApp()&&a.a.appVersion()>=9.02){var s=parseInt(e.length/2).toString(16).toLocaleUpperCase();1==s.length&&(s="0"+s);var c="4F"+s+e,u=parseInt(c.length/2).toString(16).toLocaleUpperCase();1==u.length&&(u="0"+u);var l=[{apdu:"80F00100"+u+c,sw:n}];o.a.exec("OMApi",Object(r.a)({apduList:l,type:"getSimAllInfoOnExec"},{aid:"A00000015143525300"}),(function(e){2==e.length&&e[1].resSw==n?t():i({data:e,status:"13",msg:"取消默认卡失败"})}),(function(e){var n=new Error;n.data=e,i(n)}),!0)}else{var f=new Error;f.data={status:"10",msg:"当前环境不支持cancelDefaultCard方法"},i(f)}},openChannel:function(e,n){if(a.a.appVersion()>=9.02&&a.a.isInApp())o.a.exec("OMApi",{type:"openChannelOnExec",aid:e},(function(e){n(e[0])}),(function(e){var t=new Error;t.data=e,n(t)}),!1);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持开通道方法"},n(t)}},closeChannel:function(e){if(a.a.appVersion()>=9.02&&a.a.isInApp())o.a.exec("OMApi",{type:"closeChannelOnExec"},(function(n){e(n[0])}),(function(n){var t=new Error;t.data=n,e(t)}),!1);else{var n=new Error;n.data={status:"10",msg:"当前环境不支持开通道方法"},e(n)}},transmitApdu:function(e,n){if(a.a.appVersion()>=9.02&&a.a.isInApp())o.a.exec("OMApi",{type:"transmitApduOnExec",apduList:e},(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!1);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持开通道方法"},n(t)}},getAccountIsNetWorkAccount:function(e){return a.a.isInApp()&&a.a.appVersion()>=8.09?new Promise((function(n,t){o.a.exec("getAccountIsNetWorkAccount",Object(r.a)({},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用getAccountIsNetWorkAccount方法。",n(t)}))},getTestTicket:function(e){var n={url:"https://client.10010.com/edop_test/getTicketByNative",method:"GET",requestDataType:"FORM",data:{token:e.ecsToken,appId:e.appId}};return Object(s.a)(n)},wxRequestPayment:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.0201?new Promise((function(n,t){var r=G.isAndroid()?{params:e}:e;o.a.exec("wxRequestPayment",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用wxRequestPayment。",n(t)}))},aliRequestPayment:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.0201?new Promise((function(n,t){var r=G.isAndroid()?{params:e}:e;o.a.exec("aliRequestPayment",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用aliRequestPayment。",n(t)}))},unicomPay:function(e){return a.a.isInApp()&&a.a.appVersion()>=10?new Promise((function(n,t){o.a.exec("unicomPay",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用unicomPay",n(t)}))},unicomPayChannel:function(e){return a.a.isInApp()&&a.a.appVersion()>=10?new Promise((function(n,t){o.a.exec("unicomPayChannel",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用unicomPayChannel",n(t)}))},chooseCity:function(){return a.a.isInApp()&&a.a.appVersion()>=10.02?new Promise((function(e,n){o.a.exec("chooseCity",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用chooseCity",n(t)}))},chooseCityResult:function(e){if(a.a.isInApp()&&a.a.appVersion()>=11&&e)return o.a.execSync("chooseCityResult",e);var n=new Error;return n.data="当前环境不支持调用chooseCityResult",n},openAndTransmitInstructionArrayToSim:function(e,n){B=[];var t=[];e.forEach((function(e,n){0==e.apdu.indexOf("00A40400")&&t.push(n)})),console.log("indexArr",t.length);var r=[];t.forEach((function(n,a){a+1<t.length?r.push(e.slice(n,t[a+1])):r.push(e.slice(n,e.length))})),1==t.length&&r.push(e),console.log("groupArr",r.length);var a=[];r.forEach((function(e){var n="",t=[];e.forEach((function(e,r){if(0==r){var a=parseInt(e.apdu.substring("00A40400".length,"00A40400".length+2),16);n=e.apdu.substring("00A40400".length+2,"00A40400".length+2+2*a)}else t.push(e)})),a.push({apduList:t,aid:n})})),console.log("instructionGroup",a.length),function e(n,t,r){if(t.length==n)r();else{var a=t[n];console.log("transmitArrparams",a),j(a,(function(o){a.apduList.length+1==o.length&&"0000"==o[o.length-1].status?(B.push(o),e(n+1,t,r)):1==o.length?r({status:o[0].status,msg:o[0].msg,index:n}):r({status:"12",msg:o[o.length-1].msg,position:o.length-1,index:n})}))}}(0,a,(function(e){B.length==a.length?n(B):n(e)}))},sendDataByTcpSocket:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.03?new Promise((function(n,t){o.a.exec("sendDataByTcpSocket",Object(r.a)({type:"sendDataByTcpSocket"},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}),!0)})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持sendDataByTcpSocket方法",n(t)}))},yunShanFuRequestPayment:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.03?new Promise((function(n,t){o.a.exec("yunShanFuRequestPayment",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用yunShanFuRequestPayment。",n(t)}))},openKeyboard:function(e){if(a.a.isInApp()&&a.a.appVersion()>=9.0201)return new Promise((function(n,t){var r={emoji:e&&e.emoji?e.emoji:"no"};o.a.exec("openKeyboard",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}));console.warn("当前环境不支持调用openKeyboard。")},hideKeyboard:function(){if(a.a.isInApp()&&a.a.appVersion()>=9.0201)return new Promise((function(e,n){o.a.exec("hideKeyboard",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))}));console.warn("当前环境不支持调用hideKeyboard。")},securityGetInfo:function(e,n){return a.a.isInApp()?new Promise((function(t,r){if(a.a.appVersion()>=9.0201){var i={action:"getInfo",params:{businessCode:e,businessDesc:n}};o.a.exec("DTFGAuthentication",i,(function(e){t(e)}),(function(e){var n=new Error;n.data=e,r(n)}))}else{var s=new Error;s.code="13",t(s)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用securityGetInfo方法。",n(t)}))},securitySet:function(e){return a.a.isInApp()?new Promise((function(n,t){if(a.a.appVersion()>=9.0201){var r={action:"set",params:{businessKey:e}};o.a.exec("DTFGAuthentication",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else{var i=new Error;i.code="13",n(i)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用securitySet方法。",n(t)}))},securityAuthentication:function(e){return a.a.isInApp()?new Promise((function(n,t){if(a.a.appVersion()>=9.0201){var r={action:"authentication",params:{businessKey:e}};o.a.exec("DTFGAuthentication",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else{var i=new Error;i.code="13",n(i)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用securityAuthentication方法。",n(t)}))},securityCancel:function(e){return a.a.isInApp()?new Promise((function(n,t){if(a.a.appVersion()>=9.0201){var r={action:"cancel",params:{businessKey:e}};o.a.exec("DTFGAuthentication",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))}else{var i=new Error;i.code="13",n(i)}})):new Promise((function(e,n){var t=new Error;t.data="端外不支持调用securityGetInfo方法。",n(t)}))},disConnectTCPSocketServer:function(){return a.a.isInApp()&&a.a.appVersion()>=9.03?new Promise((function(e,n){o.a.exec("sendDataByTcpSocket",{type:"disConnectServer"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}),!0)})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持disConnectTCPSocketServer方法",n(t)}))},poiList:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){o.a.exec("poiList",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持poiList方法"},n(t)})):(console.warn("端外不支持调用poiList方法。"),new Promise((function(e){e({code:"0000",data:[{name:"岭南路36号院内",address:"北京市海淀区北洼路62号院-10号楼",latitude:"39.93348275701386",longitude:"116.30880343899985"},{name:"国图文化大厦",address:"北京市海淀区北洼路63号院",latitude:"39.93348275701386",longitude:"116.30880343899985"}]})})))},choosePoi:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){o.a.exec("choosePoi",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持choosePoi方法"},n(t)})):(console.warn("端外不支持调用choosePoi方法。"),new Promise((function(e){e({code:"0000",locationInfo:{name:"岭南路36号院内",address:"北京市海淀区北洼路62号院-10号楼",latitude:"39.93348275701386",longitude:"116.30880343899985",province:"北京市",city:"北京市",district:"海淀区",town:"八里庄街道",streetName:"北洼路",streetNumber:"62号院-10号楼",adCode:"110108"}})})))},chooseLocation:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){o.a.exec("chooseLocation",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持chooseLocation方法"},n(t)})):(console.warn("端外不支持调用chooseLocation方法。"),new Promise((function(e){e({code:"0000",locationInfo:{name:"岭南路36号院内",address:"北京市海淀区北洼路62号院-10号楼",latitude:"39.93348275701386",longitude:"116.30880343899985",province:"北京市",city:"北京市",district:"海淀区",town:"八里庄街道",streetName:"北洼路",streetNumber:"62号院-10号楼",adCode:"110108"}})})))},locationSearchTips:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){console.log(JSON.stringify(e)),o.a.exec("locationSearchTips",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持locationSearchTips方法"},n(t)})):(console.warn("端外不支持调用locationSearchTips方法。"),new Promise((function(e){e({code:"0000",locationList:[{name:"岭南路36号院内",address:"北京市海淀区北洼路62号院-10号楼",latitude:"39.93348275701386",longitude:"116.30880343899985"},{name:"国图文化大厦",address:"北京市海淀区北洼路63号院",latitude:"39.93348275701386",longitude:"116.30880343899985"}]})})))},reverseGeocoding:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){console.log(JSON.stringify(e)),o.a.exec("reverseGeocoding",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持reverseGeocoding方法"},n(t)})):(console.warn("端外不支持调用reverseGeocoding方法。"),new Promise((function(e){e({code:"0000",reverseGeoInfo:{name:"岭南路36号院内",address:"北京市海淀区北洼路62号院-10号楼",latitude:"39.93348275701386",longitude:"116.30880343899985",province:"北京市",city:"北京市",district:"海淀区",town:"八里庄街道",streetName:"北洼路",streetNumber:"62号院-10号楼",adCode:"110108"}})})))},geocoding:function(e){return a.a.isInApp()?a.a.appVersion()>=9.04?new Promise((function(n,t){console.log(JSON.stringify(e)),o.a.exec("geocoding",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"30",msg:"9.4以下版本不支持geocoding方法"},n(t)})):(console.warn("端外不支持调用geocoding方法。"),new Promise((function(e){e({code:"0000",geoInfo:{precise:"1",confidence:"75",latitude:"39.93348275701386",longitude:"116.30880343899985"}})})))},chooseMedia:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.04?new Promise((function(n,t){i({scope:"scope.album",action:"chooseMedia"}).then((function(){console.log("调用chooseMedia，自动请求scope.album授权，验证通过");var r={mediaType:e&&e.mediaType?e.mediaType:"image",count:e&&e.count?e.count:9,compress:e&&e.compress?e.compress:"yes",crop:e&&e.crop?e.crop:"yes",compressSize:100};o.a.exec("chooseMedia",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})).catch((function(e){console.log("调用chooseMedia，自动请求scope.album授权，验证拒绝"),t(e)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用chooseMedia",n(t)}))},createHiddenWebView:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.03?new Promise((function(n,t){o.a.exec("createHiddenWebView",Object(r.a)(Object(r.a)({},e),{},{type:"hiddenWebViewAssignmentUrl"}),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用createHiddenWebView。",n(t)}))},getHiddenWebViewUrl:function(){return a.a.isInApp()&&a.a.appVersion()>=9.03?new Promise((function(e,n){o.a.exec("createHiddenWebView",{url:"",type:"hiddenWebViewWhetherHaveUrl"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用getHiddenWebViewUrl。",n(t)}))},stopBluetoothDevicesDiscovery:function(){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(e,n){o.a.exec("CUBluetoothManager",{type:"stopBluetoothDevicesDiscovery"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持stopBluetoothDevicesDiscovery方法"},n(t)}))},startBluetoothDevicesDiscovery:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"startBluetoothDevicesDiscovery",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持startBluetoothDevicesDiscovery方法"},n(t)}))},openBluetoothAdapter:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"openBluetoothAdapter",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持openBluetoothAdapter方法"},n(t)}))},onBluetoothDeviceFound:function(e){if(a.a.isInApp()&&a.a.appVersion()>=9.06)return o.a.exec("CUBluetoothManager",{type:"onBluetoothDeviceFound"},(function(){}),(function(){})),o.a.addEventListener("onBluetoothDeviceFound",e);var n=new Error;n.data={status:"10",msg:"当前环境不支持openBluetoothAdapter方法"},e(n)},onBluetoothAdapterStateChange:function(e){if(a.a.isInApp()&&a.a.appVersion()>=9.06)return o.a.exec("CUBluetoothManager",{type:"onBluetoothAdapterStateChange"},(function(){}),(function(){})),o.a.addEventListener("onBluetoothAdapterStateChange",e);var n=new Error;n.data={status:"10",msg:"当前环境不支持openBluetoothAdapter方法"},e(n)},offBluetoothDeviceFound:function(e){a.a.isInApp()&&a.a.appVersion()>=9.06?(o.a.exec("CUBluetoothManager",{type:"offBluetoothDeviceFound"},(function(){}),(function(){})),o.a.removeEventListener("onBluetoothDeviceFound",e)):console.log({status:"10",msg:"当前环境不支持openBluetoothAdapter方法"})},offBluetoothAdapterStateChange:function(e){a.a.isInApp()&&a.a.appVersion()>=9.06?(o.a.exec("CUBluetoothManager",{type:"offBluetoothAdapterStateChange"},(function(){}),(function(){})),o.a.removeEventListener("onBluetoothAdapterStateChange",e)):console.log({status:"10",msg:"当前环境不支持openBluetoothAdapter方法"})},makeBluetoothPair:function(e){return a.a.isAndroid()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"makeBluetoothPair",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持makeBluetoothPair方法"},n(t)}))},isBluetoothDevicePaired:function(e){return a.a.isAndroid()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"isBluetoothDevicePaired",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持isBluetoothDevicePaired方法"},n(t)}))},getConnectedBluetoothDevices:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"getConnectedBluetoothDevices",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持getConnectedBluetoothDevices方法"},n(t)}))},getBluetoothDevices:function(){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(e,n){o.a.exec("CUBluetoothManager",{type:"getBluetoothDevices"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持getBluetoothDevices方法"},n(t)}))},getBluetoothAdapterState:function(e){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(n,t){o.a.exec("CUBluetoothManager",{type:"getBluetoothAdapterState",params:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持getBluetoothAdapterState方法"},n(t)}))},closeBluetoothAdapter:function(){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(e,n){o.a.exec("CUBluetoothManager",{type:"closeBluetoothAdapter"},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持closeBluetoothAdapter方法"},n(t)}))},getAddressBookByGroup:function(){return a.a.isInApp()&&a.a.appVersion()>=9.06?new Promise((function(e,n){i({scope:"scope.contacts",action:"getAddressBookByGroup"}).then((function(){console.log("调用getAddressBookByGroup，自动请求scope.contacts授权，验证通过"),o.a.exec("getAddressBookByGroup",{},(function(n){e(n)}),(function(e){var t=new Error;t.data=e,n(t)}))})).catch((function(e){console.log("调用getAddressBookByGroup，自动请求scope.contacts授权，验证拒绝"),n(e)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持getAddressBookByGroup方法"},n(t)}))},pullUpTheApplicationMarket:function(){a.a.appVersion()>=9&&a.a.isInApp()&&ms.exec("evaluationUnicom",{type:"05",sourceType:"02",number:"50"}).then((function(e){console.log(e,"-----------")})).catch((function(e){console.log(e,"+++++++++++++++")}))},openIOSWebviewBounces:function(e){return a.a.isIOS()&&a.a.appVersion()>=10.01?new Promise((function(n,t){o.a.exec("closeWebviewBounces",{bounces:e},(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用openWebviewBounces。",n(t)}))},postMessage:function(e){return a.a.isInApp()&&a.a.appVersion()>=10.01?new Promise((function(n,t){o.a.exec("postMessage",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用postMessage",n(t)}))},onMessage:function(e){return o.a.addEventListener("onMessage",e)},offMessage:function(e){o.a.removeEventListener("onMessage",e)},rsaJiaMi:function(e,n){if(a.a.isInApp()&&a.a.appVersion()>=10)o.a.exec("RSA",Object(r.a)({type:"1"},e),(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!0);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持rsaJiaMi方法"},n(t)}},rsaVerify:function(e,n){if(console.log("RSA验签方法 传RSA待验文本、RSA签名文本、公钥"),a.a.isInApp()&&a.a.appVersion()>=10)o.a.exec("RSA",Object(r.a)({type:"3"},e),(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!0);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持rsaVerify方法"},n(t)}},openCardCamera:function(e,n,t){if(a.a.isInApp()&&a.a.appVersion()>=10.01){var r={logTitle:e};o.a.exec("cardCamera",r,(function(e){n(e)}),(function(e){t(e)}),!0)}else{var i=new Error;i.data={status:"10",msg:"当前环境不支持openCardCamera方法"},t(i)}},openCustomCamera:function(e,n,t){if(a.a.isInApp()&&a.a.appVersion()>=11.05)o.a.exec("customCamera",e,(function(e){n(e)}),(function(e){t(e)}),!0);else{var r=new Error;r.data={status:"10",msg:"当前环境不支持openCustomCamera方法"},t(r)}},subscribeMessage:function(e){return a.a.isInApp()&&a.a.appVersion()>=10.02&&G.isEdop()?new Promise((function(n,t){o.a.exec("subscribeMessage",{tcId:e.tcId},(function(){n()}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用subscribeMessage方法，需要在edop环境，最低支持APP10.2版本。",n(t)}))},openUrlScheme:function(e){return a.a.appVersion()>=10.02&&a.a.isInApp()?new Promise((function(n,t){o.a.exec("openUrlScheme",Object(r.a)({},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用openUrlScheme。",n(t)}))},openPhoneDefaultBrowser:function(e){return a.a.isInApp()&&a.a.appVersion()>=10.06?new Promise((function(n,t){var r={};e&&(r=e),o.a.exec("openPhoneDefaultBrowser",r,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"10",msg:"当前环境不支持openPhoneDefaultBrowser方法"},n(t)}))},getEdopAppCurrentEnvironment:function(){return a.a.isInApp()&&a.a.isEdop()&&a.a.appVersion()>=11.02?o.a.execSync("edopAppCurrentEnvironment"):"1"},vConsolecreatePlugin:H.a,huidu:K,remoteVideoWeatherOffCamera:function(e){return a.a.isInApp()&&a.a.appVersion()>=10.02?new Promise((function(n,t){o.a.exec("newWebRtc",Object(r.a)({action:"remoteVideoWeatherOffCamera"},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用remoteVideoWeatherOffCamera。",n(t)}))},hiddenWebRtcView:function(e){return a.a.isInApp()&&a.a.appVersion()>=10.02?new Promise((function(n,t){o.a.exec("newWebRtc",Object(r.a)({action:"hiddenWebRtcView"},e),(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data="当前环境不支持调用hiddenWebRtcView",n(t)}))},onEdopCollectionStatusChanged:function(e){return o.a.addEventListener("onEdopCollectionStatusChanged",e)},offEdopCollectionStatusChanged:function(e){o.a.removeEventListener("onEdopCollectionStatusChanged",e)},AudioPlayer:A,createAudioPlayer:function(){if(a.a.isInApp()&&a.a.appVersion()>=10.04)return new A;console.warn("当前环境不支持调用creatAudioPlayer。")},aesCbcEncrypt:function(e,n){if(a.a.isInApp()&&a.a.appVersion()>=10.08)o.a.exec("AesCbcCommon",Object(r.a)({type:"1"},e),(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!0);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持aesCbcEncrypt方法"},n(t)}},aesCbcDecrypt:function(e,n){if(a.a.isInApp()&&a.a.appVersion()>=10.08)o.a.exec("AesCbcCommon",Object(r.a)({type:"2"},e),(function(e){n(e)}),(function(e){var t=new Error;t.data=e,n(t)}),!0);else{var t=new Error;t.data={status:"10",msg:"当前环境不支持aesCbcDecrypt方法"},n(t)}},openExternalMap:function(e){return a.a.isInApp()&&a.a.appVersion()>=11.03?new Promise((function(n,t){o.a.exec("openExternalMap",e,(function(e){n(e)}),(function(e){var n=new Error;n.data=e,t(n)}))})):new Promise((function(e,n){var t=new Error;t.data={status:"13",msg:"当前环境不支持调用openExternalMap"},n(t)}))},crossPageMessagePost:function(e,n,t){var r=null;if(0==a.a.isInApp()&&(r="当前环境不支持原生跨页面消息通讯方法"),a.a.appVersion()<11.06&&(r="当前版本不支持原生跨页面消息通讯方法"),r)return new Promise((function(e,n){var t=new Error;t.data={data:r},n(t)}));var o="*";return t&&t.length>0&&(o=t),ms.exec("nativeCrossPageMessageSend",{keyPath:e,data:n,domain:o})},crossPageMessageRegister:function(e,n){var t=null;return 0==a.a.isInApp()&&(t="当前环境不支持原生跨页面消息通讯方法"),a.a.appVersion()<11.06&&(t="当前版本不支持原生跨页面消息通讯方法"),t?new Promise((function(e,n){var r=new Error;r.data={data:t},n(r)})):(o.a.exec("nativeCrossPageMessageRegist",{keyPath:e}),o.a.addEventListener(e,n))},crossPageMessageRemove:function(e,n){var t=null;if(0==a.a.isInApp()&&(t="当前环境不支持原生跨页面消息通讯方法"),a.a.appVersion()<11.06&&(t="当前版本不支持原生跨页面消息通讯方法"),t)return new Promise((function(e,n){var r=new Error;r.data={data:t},n(r)}));o.a.removeEventListener(e,n)}});window.ms=G;n.default=G}]).default}));