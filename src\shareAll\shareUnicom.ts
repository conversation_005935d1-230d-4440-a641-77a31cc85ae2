import { loadMs, ms } from '../UnicomClientApi'
import { IShareOptions } from '../types'

export default (options: IShareOptions, userData?: any) => {
  const { link, title, describe, picUrl, channel } = options

  let channelArr: string[]

  if (channel && channel.length > 0) {
    channelArr = channel
  } else {
    channelArr = 'wechat,wechatmoments,qq,qzone,sinaweibo,shortmessage,wokouling,huabaofenxiang'.split(',')
  }

  loadMs().then(() => {
    ms.showShareMenu({
      shareStyle: 'webpage', // image/webpage/pictorial
      shareTitle: title,
      shareContent: describe,
      shareIconURL: picUrl,
      shareURL: link,
      shareChannel: channelArr
    })
  })
}
