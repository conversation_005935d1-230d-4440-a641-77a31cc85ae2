import axios from 'axios'
import { stringify as qsStringify } from 'qs'
import { isEqual, cloneDeep } from 'lodash'
import { woReport } from './reporter'
import { log } from './log'
import { ILoginH5Resp, ILoginH5RespWrapped, IFormPostLoginCallback, IRequestCachedData } from '../types'

export { axios }

axios.defaults.withCredentials = true // 跨站带上 cookie 信息
axios.defaults.timeout = 30 * 1000 // 超时时间，单位ms

const jsonHeaders = { 'Content-Type': 'application/json;charset=UTF-8' }

const axiosSend = (options: any) => new Promise<any>((resolve, reject) => {
  axios(options).then(res => {
    const json = res.data
    resolve(json)
  }).catch(err => {
    // 如果是上报接口，则忽略
    if (options.url && options.url.indexOf('/itf/bscorefront/api/log/record') >= 0) return
    // 错误上报
    woReport('AJAX_ERR', `${options.url}:${JSON.stringify(options.data)}:${err}`)
    reject(err)
  })
})

/**
 * json 形式 post 请求
 * @param url
 * @param data
 * @param headers
 * @param config
 */
export const jsonPost = (url: string, data: any = {}, headers: any = {}, config: any = {}) => {
  const sendData = Object.assign({}, data, { _t: Math.random() })
  const customHeaders = Object.assign({}, jsonHeaders, headers)
  const options = Object.assign({}, { url, method: 'post', headers: customHeaders, data: sendData }, config)

  return axiosSend(options)
}

/**
 * 自有 formPost 请求的缓存
 */
const requestCached: IRequestCachedData[] = []

/**
 * 把 requestCached 中完成的任务进行回调
 */
const requestCachedCallback = () => {
  requestCached.forEach(item => {
    if (item.resp) {
      log('formPostLoginCallback', item)
      while (item.callback.length > 0) {
        const cb = item.callback.shift()
        cb && cb(item.resp)
      }
    }
  })
}

/**
 * 自有登录接口请求（带缓存）
 * @param url
 * @param data
 * @param headers
 * @param config
 * @param next
 */
export const formPostLogin = (url: string, data: any = {}, headers: any = {}, config: any = {}, next: IFormPostLoginCallback) => {
  log('formPostLogin 登录', data)
  // 1. 将请求放入缓存
  // 检查当前请求，是否存在缓存数据
  const itemCache = requestCached
    // 过滤相同的请求
    .filter(item => {
      return item.url === url &&
        item.data.source === data.source &&
        item.data.rptid === data.rptid &&
        item.data.ticket === data.ticket &&
        item.data.unionSessionId === data.unionSessionId
    })[0]

  if (itemCache) {
    // 存在缓存数据
    if (itemCache.resp) {
      // 存在缓存数据，且有结果
      if (Number(new Date()) - Number(itemCache.time) < 2000) {
        // 存在缓存数据，且有结果，且在 2s 有效期内，直接回调
        log('formPostLogin 登录，当前存在数据及缓存有效，回调')
        next(itemCache.resp)
      } else {
        // 存在缓存数据，且有结果，不在 2s 有效期内，直接重新全量缓存数据
        log('formPostLogin 登录，当前存在数据但缓存失效，生成')
        const cachedItem: IRequestCachedData = {
          url,
          data,
          headers,
          config,
          resp: null,
          callback: [next],
          time: new Date(),
          isLock: false
        }
        requestCached.push(cachedItem)
      }
    } else {
      // 存在缓存数据，无结果
      log('formPostLogin 登录，当前存在缓存且无结果，追加模式')
      itemCache.callback.push(next)
    }
  } else {
    // 不存在缓存，全量保存缓存数据
    log('formPostLogin 登录，当前无缓存，生成')
    const cachedItem: IRequestCachedData = {
      url,
      data,
      headers,
      config,
      resp: null,
      callback: [next],
      time: new Date(),
      isLock: false
    }
    requestCached.push(cachedItem)
  }

  // 2. 准备启动发送请求
  requestCached
    // 获取未发送的缓存数据
    .filter(item => !item.isLock)
    // 处理请求
    .forEach(item => {
      item.isLock = true
      item.data._t = Math.random()
      const customHeaders = Object.assign({}, item.headers)
      const postData = qsStringify(item.data, { indices: false })
      const options = Object.assign({}, { url: item.url, method: 'post', headers: customHeaders, data: postData }, item.config)
      log('formPostLogin 发起登录', item)
      axiosSend(options).then((json: ILoginH5Resp | ILoginH5RespWrapped) => {
        const result: ILoginH5Resp = (json as ILoginH5RespWrapped).data ? (json as ILoginH5RespWrapped).data : (json as ILoginH5Resp)
        // 缓存数据
        item.resp = [null, result]
        // 触发回调
        requestCachedCallback()
      }).catch(e => {
        item.resp = [e, null]
        // 触发回调
        requestCachedCallback()
      })
    })
}

/**
 * form 表单形式 post 请求
 * @param url
 * @param data
 * @param headers
 * @param config
 */
export const formPost = (url: string, data: any = {}, headers: any = {}, config: any = {}) => {
  const sendData = Object.assign({}, data, { _t: Math.random() })
  const customHeaders = Object.assign({}, headers)
  const postData = qsStringify(sendData, { indices: false })
  const options = Object.assign({}, { url, method: 'post', headers: customHeaders, data: postData }, config)
  return axiosSend(options)
}

/**
 * form 表单形式 get 请求
 * @param url
 * @param data
 * @param headers
 * @param config
 */
export const formGet = (url: string, data: any = {}, headers: any = {}, config: any = {}) => {
  const sendData = Object.assign({}, data, { _t: Math.random() })
  const customHeaders = Object.assign({}, headers)
  const options = Object.assign({}, { url, method: 'get', headers: customHeaders, params: sendData }, config)
  return axiosSend(options)
}

const store: any[] = []

const cache = {
  set (key: any, params: any, data: any) {
    const o = { key, params, data }
    store.push(o)
  },
  get (key: any, params: any) {
    const s = store.filter(i => i.key === key && isEqual(i.params, params))[0]
    if (s) {
      return s.data
    }
  }
}

/**
 * 请求缓存
 * @param fn
 */
export const cached = (fn: Function) => {
  return async (...args: any[]) => {
    try {
      const argsStr = JSON.stringify(args)
      const cacheData = cache.get(fn, argsStr)
      if (cacheData) return cloneDeep(cacheData)
      const res = await fn.apply(null, args)
      const err = res[0]
      // 成功的数据，进行缓存
      if (!err) cache.set(fn, argsStr, res)
      return res
    } catch (e) {
      return fn.apply(null, args)
    }
  }
}

/**
 * 使用 form 表单进行条数据
 * @param url
 * @param data
 */
export const formSubmit = (url: string, data: any) => {
  const id = 'submitForm'

  const oldForm = document.getElementById(id)
  if (oldForm) document.body.removeChild(oldForm)

  const form = document.createElement('form')
  form.id = id
  form.style.display = 'none'
  form.action = url
  form.method = 'post'

  for (const key in data) {
    form.innerHTML = `<input type="text" name="${key}" value="${data[key]}" />`
  }

  document.body.appendChild(form)
  form.submit()
}

export class GroupSend {
  list: Promise<any>[] = []
  count = 0

  cb (isSuccess: boolean) {
    this.count++
    if (this.count === this.list.length) {
      this.onComplete()
    }
  }

  push (promise: Promise<any>) {
    this.list.push(promise)
    return promise.then(res => {
      // 使用setTimeout，防止cb方法先调用，导致正常业务promise后调用
      setTimeout(() => this.cb(true))
      return res
    }).catch(err => {
      // 使用setTimeout，防止cb方法先调用，导致正常业务promise后调用
      setTimeout(() => this.cb(false))
      throw err
    })
  }

  size () {
    return this.list.length
  }

  onComplete () {
  }
}
