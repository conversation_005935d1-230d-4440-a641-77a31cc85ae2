/// <reference path="../types/unicom.d.ts" />
// 文档 http://ecstest2018.10010.com/yhdocs/index.html#/
// 文档 http://client.10010.com/unicomappdocs/index.html
// 引用文件 https://img.client.10010.com/stprototype/commonlibs/ms/msJSBridge-0.4.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-0.6.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-0.9.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-1.0.4.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-1.0.6.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-1.0.9.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-harmony-1.3.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-harmony-1.5.js
// 引用文件 https://img.client.10010.com/mslab/libs/msJSBridge-harmony-1.6.js
import { isUnicom } from '../env'

let ms: any

export const loadMs = () => new Promise((resolve, reject) => {
  if (ms) {
    resolve(ms)
    return
  }

  if (isUnicom) {
    // @ts-ignore
    import('./msJSBridge-harmony-1.6.js').then(() => {
      ms = window.ms
      resolve(ms)
    }).catch((err) => {
      reject(err)
    })
  } else {
    reject(new Error('load ms lib failed.'))
  }
})

export { ms }
