import UrlParse from 'url-parse'
import { stringify as qsStringify } from 'qs'
import { log } from '../_tools/log'
import { status, login } from './base'
import { getInsensitiveParams, getLoginParams, getLoginParamsForUser, shared } from './utils'
import { loginParamsStorage, loginParamsStorageForUser } from './config'
import { WopayLoginType } from './enum'
import {
  ILoginInnerResp,
  ILoginOptions,
  IVueHelperLoginRunOptions,
  IVueHelperLoginOptions,
  IVueHelperLoginState,
  IVueHelperQueryStatus,
  IVueHelperToLogin,
  IVueHelperUseLogin
} from '../types'

/**
 * 移除链接中的敏感登录字段
 * 要求页面不要刷新，故使用 window.history.replaceState
 */
const removeSensitiveParams = () => {
  console.log(`higher.ts removeSensitiveParams ${window.location.href}`)
  const parsedUrl = new UrlParse(window.location.href)
  console.log(`higher.ts removeSensitiveParams parsedUrl.query: ${parsedUrl.query}`)
  const { has, params } = getInsensitiveParams()
  if (has) {
    console.log('higher.ts removeSensitiveParams has params:', params)
    console.log('higher.ts removeSensitiveParams before qsStringify:', JSON.stringify(params))
    const newQueryString = qsStringify(params)
    console.log(`higher.ts removeSensitiveParams after qsStringify: ${newQueryString}`)
    parsedUrl.set('query', '?' + newQueryString)
    const newUrl = parsedUrl.toString()
    console.log(`higher.ts removeSensitiveParams newUrl: ${newUrl}`)
    window.history.replaceState({}, '', newUrl)
    log('已移除登录隐私参数，新地址', newUrl)
  }
}

/**
 * 缓存登录参数
 */
const cacheParams = {
  isCached: false,
  handler () {
    console.log(`higher.ts cacheParams.handler start, isCached: ${this.isCached}, URL: ${window.location.href}`)
    if (this.isCached) return
    this.isCached = true
    // 缓存一份登录信息（首次缓存数据时，不使用使用缓存中的参数，防止历史数据）
    console.log(`higher.ts cacheParams.handler before getLoginParams ${window.location.href}`)
    const loginParams = getLoginParams(false)
    console.log('higher.ts cacheParams.handler getLoginParams result:', loginParams)
    loginParamsStorage.set(loginParams)
    console.log(`higher.ts cacheParams.handler after loginParamsStorage.set ${window.location.href}`)

    // 用户级别缓存，则默认优选使用老缓存
    console.log(`higher.ts cacheParams.handler before getLoginParamsForUser ${window.location.href}`)
    const userParams = getLoginParamsForUser()
    console.log('higher.ts cacheParams.handler getLoginParamsForUser result:', userParams)
    const a = Object.assign({}, loginParamsStorageForUser.get(), userParams)
    loginParamsStorageForUser.set(a)
    console.log(`higher.ts cacheParams.handler end ${window.location.href}`)
  }
}

/**
 * 查询登录状态
 * @param options
 */
const helperStatus = (options?: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  const apiLoginPath = window.COMMONKIT_LOGIN_PATH
  log('login-helperStatus', options)
  const opts = {
    wopayLoginMode: WopayLoginType.NEED_ONCE,
    ...(options || {})
  }
  console.log(`higher.ts helperStatus start ${window.location.href}`)
  status(apiLoginPath, opts).then(json => {
    console.log(`higher.ts helperStatus end ${window.location.href}`)
    resolve(json)
  })
})

/**
 * 登录
 * @param opts notCheckLogin 是否不检查登录信息，true-不检查直接登录，false-（默认）需要先检查，再登录
 * @param options
 */
const helperLogin = (opts?: IVueHelperLoginOptions, options?: ILoginOptions) => new Promise<ILoginInnerResp>((resolve, reject) => {
  console.log(`higher.ts helperLogin ${window.location.href}`)
  const apiLoginPath = window.COMMONKIT_LOGIN_PATH
  const { notCheckLogin, replace, isLocal, callbackUrl } = opts || {}
  const fn = (res: (value: ILoginInnerResp) => void, rej: (reason?: any) => void) => {
    // useHistoryReplace 开发环境需要是false，否则无法登录
    const useHistoryReplace = isLocal ? false : replace
    const userOpts = {
      useHistoryReplace,
      wopayLoginMode: WopayLoginType.NEED_ONCE,
      callbackUrl,
      ...(options || {})
    }

    login(apiLoginPath, userOpts).then(json => {
      // 务必实现 then 方法处理成功登录的情况，以防出现意外渠道没有刷新页面
      if (json.status) {
        window.location.reload()
      } else {
        // 登录失败
        res(json)
      }
    }).catch((e) => {
      rej(e)
    })
  }

  if (notCheckLogin) {
    // 可以直接走登录逻辑
    fn(resolve, reject)
  } else {
    // 需要先检查登录，再走逻辑
    console.log(`higher.ts helperLogin before helperStatus ${window.location.href}`)
    helperStatus(options).then(json => {
      const status = json.status
      if (!status) {
        // 未登录情况
        fn(resolve, reject)
      } else {
        // 已经登录情况
        resolve(json)
      }
    }).catch((e) => {
      reject(e)
    })
  }
})

// 用户登录状态，0-未检查，-1-登录错误，1-未登录，2-已登录
const state: IVueHelperLoginState = { status: 0, cifId: '', err: '', errId: 0, zqInfo: [] }
const stateCallback: Function[] = []

const runStateCallback = () => {
  stateCallback.forEach(cb => {
    cb && cb(state)
  })
}

const queryStatus: IVueHelperQueryStatus = async (options) => {
  log('login-helperQueryStatus', options)
  console.log(`higher.ts queryStatus start ${window.location.href}`)
  if (state.status === 1) {
    // 未登录，无需处理
    console.log(`higher.ts queryStatus end (status=1) ${window.location.href}`)
    return false
  } else if (state.status === 2) {
    // 已登录，无需处理
    console.log(`higher.ts queryStatus end (status=2) ${window.location.href}`)
    return true
  } else {
    // 登录状态未明，检查登录状态
    console.log(`higher.ts helperLogin before helperStatus-2 ${window.location.href}`)
    const json = await helperStatus(options)
    console.log(`higher.ts queryStatus after helperStatus ${window.location.href}`)
    log('login-helperQueryStatus 检查登录状态', json)
    const isLogin = json.status
    log(isLogin ? 'login-helperQueryStatus 已登录' : 'login-helperQueryStatus 未登录')
    if (isLogin) {
      state.status = 2
      state.cifId = json.data.cifId
      state.zqInfo = json.data.zqInfo
    } else {
      if (json.data.netErr) {
        state.status = -1
      } else {
        state.status = 1
      }
      state.err = json.data.err
      state.errId = json.data.errId
    }

    runStateCallback()
    console.log(`higher.ts queryStatus end (after helperStatus) ${window.location.href}`)
    return isLogin
  }
}

const toLogin: IVueHelperToLogin = async (payload, options) => {
  const defaultPayload = {
    callback: () => {},
    loginType: '',
    isLocal: false,
    callbackUrl: window.location.href
  }
  console.log(`higher.ts toLogin ${defaultPayload.callbackUrl}`)

  const payloadCombined = Object.assign(defaultPayload, payload)

  // 回调方法
  const callback = payloadCombined.callback
  // 是否为登录前置
  const isLoginBefore = payloadCombined.loginType === 'before'
  // 是否为本地调试环境
  const isLocal = !!payloadCombined.isLocal
  // 回调地址
  const callbackUrl = payloadCombined.callbackUrl
  // 是否不用检查登录，false-还不知道是否登录了，需要检查，true-已经明确没有登录，直接走登录流程
  const notCheckLogin = state.status !== 0

  // 什么时候需要 replace，什么时候不用？
  // ==情形==
  // 理财A(登录后置)、不用 replace
  // 理财A(登录前置)、用 replace
  // 理财A -- 理财B(登录前置)、不用 replace
  // ==总结如下==
  // * 登录后置，一律不用 replace
  // * 登录前置，区分情况：
  //   -- 如果是没有任何操作（上来就登录），则用 replace
  //   -- 否则其他情况一般都是在理财有过页面跳转或者操作的，跳转到需要登录的页面，此时不用 replace
  // ==实现==
  // * 登录后置，必定走 Login.vue 页，此页面调用 goLogin 方法，带上了参数 loginType: 'before'
  // * 登录前置，在页面入口(main.js)存入时间戳（shared对象内），调用 goLogin 时候检查时间戳插值
  //   -- 小于一定时间，认定是入口页即登录前置，用 replace，
  //   -- 反之为后续页面（点击按钮要求登录等），不用 replace
  let replace: boolean
  if (isLoginBefore) {
    // 登录前置，区分情况
    const now = new Date().getTime()

    if (!shared.timestamp || (now - shared.timestamp < 1000)) {
      // 时间较短，说明是首次访问要求登录
      replace = true
    } else {
      // 后续页面点击按钮或者其他页面要求登录
      replace = false
    }
  } else {
    // 登录后置，不用 replace
    replace = false
  }
  try {
    try {
      const json = await helperLogin({ notCheckLogin, replace, isLocal, callbackUrl }, options)
      if (json.status) {
        state.status = 2
        runStateCallback()
        // eslint-disable-next-line n/no-callback-literal
        callback && callback(2)
      } else {
        // eslint-disable-next-line n/no-callback-literal
        callback && callback(-1)
      }
    } catch (e) {
      // eslint-disable-next-line n/no-callback-literal
      callback && callback(-1)
    }
  } catch (e) {
    state.status = -1
    runStateCallback()
    // eslint-disable-next-line n/no-callback-literal
    callback && callback(-1)
  }
}

export const useLogin: IVueHelperUseLogin = () => {
  return [state, queryStatus, toLogin]
}

export const loginRun = (opts?: IVueHelperLoginRunOptions, options?: ILoginOptions) => {
  console.log(`higher.ts loginRun start ${window.location.href}`)

  // 缓存登录参数
  console.log(`higher.ts loginRun before cacheParams.handler ${window.location.href}`)
  cacheParams.handler()
  console.log(`higher.ts loginRun after cacheParams.handler ${window.location.href}`)

  // 移除链接中的敏感登录字段
  // 需要延迟处理。否则第一次是本方法进行移除 URL中的参数，第二次为 vue-router 跳转处理。顺序错误。
  setTimeout(() => {
    console.log(`higher.ts loginRun before removeSensitiveParams ${window.location.href}`)
    removeSensitiveParams()
    console.log(`higher.ts loginRun after removeSensitiveParams ${window.location.href}`)
  }, 500)

  // 进入项目写入入口时间戳（登录控制使用）
  shared.timestamp = new Date().getTime()

  if (opts && opts.autoQueryStatus) {
    log('login-loginRun 自动触发 queryStatus')
    console.log(`higher.ts loginRun before autoQueryStatus ${window.location.href}`)
    queryStatus(options).then()
  }

  console.log(`higher.ts loginRun end ${window.location.href}`)
}

export const loginCallback = (cb: Function) => {
  stateCallback.push(cb)
}
