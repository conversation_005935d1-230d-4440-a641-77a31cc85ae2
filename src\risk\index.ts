import { log } from '../_tools/log'
import { getRiskId } from './wo-fobid-all'
import { IRiskCallbackParams, IRiskParams } from '../types'

function setCookie (name: string, value: string, options: any) {
  options = options || {}

  let expires = options.expires

  if (typeof expires === 'number' && expires) {
    const d = new Date()
    d.setTime(d.getTime() + expires * 1000)
    expires = options.expires = d
  }

  if (expires && expires.toUTCString) {
    options.expires = expires.toUTCString()
  }

  value = encodeURIComponent(value)

  let updatedCookie = name + '=' + value

  for (const propName in options) {
    updatedCookie += '; ' + propName
    const propValue = options[propName]
    if (propValue !== true) {
      updatedCookie += '=' + propValue
    }
  }

  document.cookie = updatedCookie
}

// 是否已经获取到token
let hasToken = false

const cacheToken: IRiskParams = {
  tdTokenId: '',
  unicomTokenId: '',
  tdBlackbox: ''
}

export const risk = (next?: IRiskCallbackParams, isProd?: boolean, timeout?: number) => {
  if (hasToken) {
    next && next(cacheToken, 0)
    return
  }

  const URL_PROD = 'https://media.cdn.unicompayment.com/ts-sss/oss/public/fingerprint/fp-pro/fobid-all.js'
  const URL_TEST = 'https://res.cdn.unicompayment.com/media/ts-sss/oss/public/fingerprint/fp-test/fobid-all.js'
  const url = isProd ? URL_PROD : URL_TEST

  getRiskId(url, timeout).then(res => {
    log('risk 风控完成', res)
    const { data, status } = res
    if (status === 0) {
      setCookie('CK_TD_TOKENID', data.tdTokenId, { path: '/' })
      setCookie('CK_UNICOM_TOKENID', data.unicomTokenId, { path: '/' })
      // 缓存数据，避免重复请求
      hasToken = true
      cacheToken.tdTokenId = data.tdTokenId
      cacheToken.unicomTokenId = data.unicomTokenId
      cacheToken.tdBlackbox = data.tdBlackbox
      next && next(data, status)
    } else {
      log('risk 风控失败', res)
      next && next(data, status)
    }
  })
}
