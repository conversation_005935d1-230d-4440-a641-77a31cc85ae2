import { loadMs, ms } from '../UnicomClientApi'
import { log } from '../_tools/log'
import { wqbH5Status, wqbH5Login } from './api-h5'
import { unicomLoginPhoneNoStorage } from './config'
import { RptidUpdateType } from './enum'
import { ILoginInnerResp, ILoginOptions } from '../types'

/**
 * 获取当前联通登录的手机号
 * @returns {string|*}
 */
const getCurLoginPhoneNo = () => {
  const clientUserInfo = ms.getClientInfoSync()
  return clientUserInfo.currentPhoneNumber
}

// 登录全流程
const fullLoginFn = (options: ILoginOptions, resolve: any) => {
  log('login-unicom 走登录全流程，走h5登录')
  wqbH5Login(options).then((json) => {
    resolve(json)
  }).catch(() => {
    resolve({ status: false, data: { redirectUrl: '', cifId: '', zqInfo: [] } })
  })
}

/**
 * 手厅登录验证(含预加载ms库)
 * @param opts
 */
export const unicomStatus = (opts: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  // 当前手厅登录手机号
  log('login-unicom 查询登录状态')
  loadMs().then(() => {
    const resolveFn = (data: ILoginInnerResp) => {
      // 保存登录的手机号，后续检查登录手机号用到
      const curLoginPhoneNo = getCurLoginPhoneNo()
      log(`login-unicom 登录操作完成，当前手机号：${curLoginPhoneNo}`)
      if (data.status) {
        unicomLoginPhoneNoStorage.set(curLoginPhoneNo)
      }
      resolve(data)
    }

    const isLogin = ms.isLogin()
    const options = Object.assign({}, opts, { customParams: { source: 'app_sjyyt' } })
    // 通过sdk判断是否登录
    if (isLogin) {
      // 手厅已登录，此时要求必须我方账号登录成功
      log('login-unicom 手厅已登录')
      // 先判断当前是否更换过手机号，没有更换手机号直接与后台通讯查询登录状态
      const phoneNo = unicomLoginPhoneNoStorage.get()
      const curLoginPhoneNo = getCurLoginPhoneNo()
      if (phoneNo === curLoginPhoneNo) {
        // 手机号相同，直接调用后端接口检查登录状态
        log('login-unicom 手厅已登录，相同手机号')
        wqbH5Status({ ...options, ...{ rptidUpdate: RptidUpdateType.FORCE_NOT_UPDATE } }).then((json) => {
          if (json.status) {
            // 登录成功，回调即可
            log('login-unicom 手厅已登录，相同手机号，同步登录完成')
            resolveFn(json)
          } else {
            // 没有登录，走全量流程
            log('login-unicom 手厅已登录，相同手机号，同步登录失败')
            fullLoginFn(options, resolveFn)
          }
        }).catch(() => {
          // 逻辑失败，走全量流程
          fullLoginFn(options, resolveFn)
        })
      } else {
        // 手机号不同，此时用户已登录，走全量登录流程
        log('login-unicom 手厅已登录，不同手机号')
        fullLoginFn(options, resolveFn)
      }
    } else {
      // 手厅未登录，直接回调即可
      log('login-unicom 手厅未登录')
      resolveFn({
        status: false,
        data: {
          redirectUrl: '',
          cifId: '',
          zqInfo: [],
          err: '[COMMONKIT] unicom-login: unicom app is not login yet.',
          errId: 301,
          netErr: false
        }
      })
    }
  })
})

/**
 * 手厅登录(含预加载ms库)
 * @param opts
 */
export const unicomLogin = (opts: ILoginOptions) => new Promise<ILoginInnerResp>((resolve) => {
  log('login-unicom 去登录')
  loadMs().then(() => {
    const options = Object.assign(opts, { customParams: { source: 'app_sjyyt' } })
    fullLoginFn(options, resolve)
  })
})
